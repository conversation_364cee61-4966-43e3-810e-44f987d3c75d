import React, { useState, useEffect } from 'react'
import { UseFormRegister, UseFormWatch, UseFormSetValue, FieldErrors } from "react-hook-form";
import { ScanFormData } from './PatientRecords';
import { toast } from 'react-toastify';
import { PatientFileData } from '@/types/types';

interface GeneralRecordsProps {
    register: UseFormRegister<ScanFormData>;
    watch: UseFormWatch<ScanFormData>;
    setValue: UseFormSetValue<ScanFormData>;
    errors: FieldErrors<ScanFormData>;
    patientData?: PatientFileData | null;
}

const GeneralRecords = ({ patientData, watch, setValue, errors }: GeneralRecordsProps) => {
    console.log("🚀 ~ GeneralRecords ~ patientData:", patientData)
    const [displayedFiles, setDisplayedFiles] = useState<File[]>([]);

    // Watch the files from the parent form
    const files = watch("generalRecords.files");

    // Update displayed files when files change
    useEffect(() => {
        if (files) {
            setDisplayedFiles(files.filter((f): f is File => f instanceof File));
        }
    }, [files]);

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files) {
            const fileArray = Array.from(e.target.files);
            const newFiles = [...displayedFiles, ...fileArray];

            // Limit to 10 files
            if (newFiles.length > 10) {
                toast.error("You can only upload up to 10 files.");
                const trimmedFiles = newFiles.slice(0, 10);
                setDisplayedFiles(trimmedFiles);
                setValue("generalRecords.files", trimmedFiles);
            } else {
                setDisplayedFiles(newFiles);
                setValue("generalRecords.files", newFiles);
            }
        }
    };

    const removeFile = (index: number) => {
        const newFiles = [...displayedFiles];
        newFiles.splice(index, 1);
        setDisplayedFiles(newFiles);
        setValue("generalRecords.files", newFiles);
    };

    return (
        <div className="mb-10 ">
            <h2 className="text-2xl font-bold text-dark mb-6 flex items-center">
                General Records
            </h2>

            <div className="">
                <div className="mb-4">
                    <label className="block mb-2 text-sm font-medium text-gray-700">
                        Upload Files ({displayedFiles.length}/10)
                    </label>
                    <div className="flex items-center">
                        <input
                            type="file"
                            multiple
                            onChange={handleFileChange}
                            className="block w-full text-sm text-gray-700 border border-gray-300 rounded-md cursor-pointer bg-gray-50 focus:outline-none focus:border-[#EB6309] focus:ring-2 focus:ring-[#EB6309] file:mr-4 file:py-2 file:px-4 file:border-0 file:bg-blue-50 file:text-[#EB6309]file:font-medium "
                            disabled={displayedFiles.length >= 10}
                        />
                    </div>
                    {errors.generalRecords?.files && (
                        <p className="mt-2 text-red-500 flex items-center text-sm">
                            {errors.generalRecords.files.message}
                        </p>
                    )}
                    <p className="mt-2 text-xs text-gray-500">
                        Accepted file types: PDF, DOC, DOCX, JPG, PNG, etc.
                    </p>
                </div>

                {displayedFiles.length > 0 && (
                    <div className="pt-2">
                        <h3 className="text-md font-semibold mb-3 text-gray-700 flex items-center">
                            Uploaded Files
                        </h3>
                        <ul className="divide-y divide-gray-100 rounded-md border border-gray-200">
                            {displayedFiles.map((file, index) => (
                                <li key={index} className="flex items-center justify-between py-3 px-4 hover:bg-gray-50">
                                    <div className="flex items-center flex-1 min-w-0">
                                        <svg className="w-5 h-5 mr-3 text-[#EB6309]" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clipRule="evenodd" />
                                        </svg>
                                        <span className="text-sm text-gray-700 truncate max-w-xs">{file.name}</span>
                                    </div>
                                    <button
                                        type="button"
                                        onClick={() => removeFile(index)}
                                        className="ml-4 flex-shrink-0 p-1 rounded-full text-gray-400 hover:text-red-500 focus:outline-none focus:ring-2 focus:ring-red-500"
                                    >
                                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                        </svg>
                                    </button>
                                </li>
                            ))}
                        </ul>
                    </div>
                )}
            </div>
        </div>
    )
}

export default GeneralRecords
