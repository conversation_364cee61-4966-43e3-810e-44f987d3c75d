'use client';

import Image from 'next/image';
import { useEffect, useState } from 'react';
import { usePathname } from 'next/navigation';
import getAndDecryptCookie from '@/app/lib/auth';
import { useSession } from '../sessionWrapper/SessionProvider';

interface HeaderProps {
    onSearchChange?: (value: string) => void;
    searchValue?: string;
}

const Header: React.FC<HeaderProps> = ({ onSearchChange, searchValue }) => {
    const pathname = usePathname(); // Get the current pathname
    const { user } = useSession();
    console.log("pathname-[]", pathname)

    const [localSearch, setLocalSearch] = useState(searchValue || '');
    const [dashbordpath, setDashbordpath] = useState(false);
    console.log(dashbordpath)
    const firstname = getAndDecryptCookie('first_name');
    console.log("🚀 ~ firstname:", firstname)
    const lastname = getAndDecryptCookie('last_name');
    console.log("🚀 ~ lastname:", lastname)
    // const fullName = `${firstname} ${lastname}`;
    const userId = getAndDecryptCookie('user_uuid');
    console.log("🚀 ~ userId:", userId)
    const userName = getAndDecryptCookie('username');
    console.log("🚀 ~ userName:", userName)

    useEffect(() => {
        if (
            pathname === '/dashboard'
        ) {
            setDashbordpath(true)
        } else {
            setDashbordpath(false)
        }
    }, [pathname])

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        // Update local state if searchValue is not controlled by parent
        if (!searchValue) {
            setLocalSearch(value);
        }
        // Send the search value to the parent
        onSearchChange?.(value);
    };

    return (
        <header className="flex items-center justify-between bg-[#f5f5f5] px-2 py-4">
            {/* Greeting */}
            <div className="text-2xl font-medium text-gray-800">
                {user?.role !== 'specialist' && <div>Hi 👋</div>}
            </div>
            <div className='flex items-center max-w-md'>

                <div className=" mx-4">

                    {dashbordpath && <div className="relative">
                        <input
                            type="text"
                            placeholder="Search..."
                            value={searchValue || localSearch}
                            onChange={handleSearchChange}
                            className="w-full pl-10 pr-4 py-3 rounded-full bg-white border focus:outline-none text-[#999] placeholder-[#999]"
                        />
                        <span className="absolute left-3 top-1/2 transform -translate-y-1/2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <path d="M6.5 17.5L2 22" stroke="#EB6309" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                                <path d="M4 11C4 6.02944 8.0294 2 13 2C17.9706 2 22 6.02944 22 11C22 15.9706 17.9706 20 13 20C8.0294 20 4 15.9706 4 11Z" stroke="#EB6309" strokeWidth="1.5" strokeLinejoin="round" />
                            </svg>
                        </span>
                    </div>}
                </div>

                {/* User Profile */}
                <div className="flex items-center bg-[#FFF] ps-1 pe-5 py-1 rounded-full space-x-2">
                    <Image
                        src={`https://ui-avatars.com/api/?name=${firstname}+${lastname}?background=random?rounded=true`}
                        alt="User Avatar"
                        width={1000}
                        height={1000}
                        className="w-12 h-12 rounded-full"
                    />
                    <div className="flex flex-col">
                        <span className="text-sm font-medium text-gray-800">{userName}</span>
                        <span className="text-xs text-gray-500">ID#{userId}</span>
                    </div>
                </div>
            </div>
        </header>
    );
};

export default Header;