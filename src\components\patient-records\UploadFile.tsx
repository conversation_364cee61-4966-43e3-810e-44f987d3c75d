import Image from "next/image"
import DisableOverlay from "../reuseable/DisableOverlay";
import portraitImage from "../../../public/svgs/femailAvatar.svg";
import imageFileIcon from "../../../public/svgs/icons8_image_file_add 1.svg";
import { useEffect, useState } from "react";
import { UseFormRegister, UseFormSetValue, UseFormWatch } from "react-hook-form";
import type { ScanFormData } from "./PatientRecords"
import { PatientFileData } from "@/types/types";

interface props {
    watch: UseFormWatch<ScanFormData>
    setValue: UseFormSetValue<ScanFormData>
    register: UseFormRegister<ScanFormData>
    disabled: boolean;
    name: keyof ScanFormData;
    patientData?: PatientFileData | null;
}

const UploadFile: React.FC<props> = ({ 
    name, 
    disabled = false, 
    watch, 
    setValue, 
    patientData 
}) => {
    console.log("🚀 ~ patientData:", patientData)
    const nameId = name as keyof ScanFormData;
    const [selectedFile, setSelectedFile] = useState<File | string | null>(null);
    const scan = watch("scan");

    // Watch form data for changes from parent (PatientRecords prefill)
    useEffect(() => {
        const scanData = watch("scan") as Record<string, string | File | null>;
        if (scanData && scanData[nameId]) {
            setSelectedFile(scanData[nameId]);
        }
    }, [watch("scan"), nameId]);

    useEffect(() => {
        if (disabled) {
            setSelectedFile(null);
        }
    }, [disabled]);

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files) {
            const single = e.target.files[0];
            setSelectedFile(single);
            const newObn = { ...scan, [nameId]: single };
            setValue("scan", newObn);
        }
    };

    // Helper function to get file name for display
    const getFileName = (): string => {
        if (!selectedFile) return "";
        
        if (typeof selectedFile === 'string') {
            // Extract filename from URL
            const urlParts = selectedFile.split('/');
            const fullFileName = urlParts[urlParts.length - 1];
            return fullFileName.length > 25
                ? fullFileName.slice(0, 12) + "..." + fullFileName.slice(-8)
                : fullFileName;
        }
        
        if (selectedFile instanceof File) {
            return selectedFile.name.length > 25
                ? selectedFile.name.slice(0, 12) + "..." + selectedFile.name.slice(-8)
                : selectedFile.name;
        }
        
        return "";
    };

    const hasFile = selectedFile !== null && selectedFile !== undefined;

    return (
        <div className="relative mb-7 col-span-1 h-[230px] flex flex-col items-center justify-center gap-3 !bg-primaryLight rounded-[10px]">
            <DisableOverlay active={disabled} color={"bg-black/40"} />
            {(!hasFile || disabled) ? (
                <Image 
                    src={portraitImage} 
                    alt="Placeholder" 
                    width={10000} 
                    height={1000} 
                    className="object-contain w-[80px] h-[135px]" 
                />
            ) : (
                <div className="text-dark font-semibold text-center px-2">
                    {getFileName()}
                </div>
            )}
            <div>
                <label
                    htmlFor={`file-upload-${name}`}
                    className="px-6 py-2 border border-gray text-sm text-dark rounded-full flex items-center gap-2 cursor-pointer"
                >
                    <Image src={imageFileIcon} alt="Upload icon" className="w-6 h-6" />
                    <span className="text-gray font-semibold">
                        {hasFile ? "Change File" : "Upload Zip"}
                    </span>
                </label>
                <input
                    id={`file-upload-${name}`}
                    type="file"
                    accept=".zip"
                    onChange={handleFileChange}
                    className="hidden"
                />
            </div>
        </div>
    );
};

export default UploadFile;