// src/app/api/specialistPatient.ts

import { API_ROUTES } from '@/utils/ApiRoutes';

export interface SpecialistPatient {
  id: number;
  doctor_id: number;
  first_name: string;
  last_name: string;
  email: string | null;
  dob: string;
  gender: string;
  ship_to_office: string | null;
  bill_to_office: string | null;
  plan_id: number | null;
  clinical_conditions: string[] | null;
  general_notes: string | null;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  stlFile1: string | null;
  stlFile2: string | null;
  cbctFile: string | null;
  profileRepose: string | null;
  buccalRight: string | null;
  buccalLeft: string | null;
  frontalRepose: string | null;
  frontalSmiling: string | null;
  labialAnterior: string | null;
  occlussalLower: string | null;
  occlussalUpper: string | null;
  radioGraph1: string | null;
  radioGraph2: string | null;
  data: Record<string, unknown>;
  country: string;
  uuid: string;
  plan: {
    id: number;
    name: string;
    created_at: string;
    updated_at: string;
    type: string;
    duration_years: number;
    expiration_date: string;
  };
  shipToOffice: Record<string, unknown> | null;
  billToOffice: Record<string, unknown> | null;
  refinements: {
    id: number;
    created_at: string;
    status: string;
  }[];
  alignerReplacements: {
    id: number;
    created_at: string;
    status: string;
  }[];
  retainers: {
    id: number;
    created_at: string;
    status: string;
  }[];
  refinementsAligner: {
    id: number;
    created_at: string;
    status: string;
  }[];
  patientFiles: {
    id: number;
    file_path: string;
    file_type: string;
    created_at: string;
  }[];
  status: string | null;
  versions: {
    id: number;
    version_number: number;
    created_at: string;
    status: string;
  }[];
}

interface ApiResponse<T> {
  status: number;
  success: boolean;
  data: T;
  message: string;
}

/**
 * Fetch a single patient record as a specialist
 * @param id    Patient ID to fetch
 * @param token JWT Bearer token (specialist)
 * @returns     The fully‐enriched patient object, or throws on error
 */
export async function fetchSpecialistPatient(
  id: number,
  token: string
): Promise<SpecialistPatient> {
  const url = API_ROUTES.PATIENT.GET_PATIENT_BY_ID_FOR_SPECIALIST.replace(':id', String(id));
  try {
    const res = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });
    if (!res.ok) {
      // You can inspect res.status to give more tailor‑made errors
      throw new Error(`Failed to fetch patient #${id}: ${res.statusText}`);
    }
    const json = (await res.json()) as ApiResponse<SpecialistPatient>;
    if (!json.success) {
      throw new Error(json.message || 'Unknown API error');
    }
    return json.data;
  } catch (err) {
    console.error(`Error in fetchSpecialistPatient(${id}):`, err);
    throw err;
  }
}
