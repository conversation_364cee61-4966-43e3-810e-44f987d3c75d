
import React from "react";

type Props = {
  confirmModalOpen: boolean;
  addressToDelete: {
    type: "shipping" | "billing";
    id: string;
  } | null;
  cancelDeleteAddress: () => void;
  confirmDeleteAddress: () => void;
  customMessage?: string;
};

const DeleteConfirmationModal: React.FC<Props> = ({
  confirmModalOpen,
  addressToDelete,
  cancelDeleteAddress,
  confirmDeleteAddress,
  customMessage,
}) => {
  if (!confirmModalOpen) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black/20 bg-opacity-30 z-50">
      <div className="bg-white rounded-lg shadow-lg p-6 w-[400px] flex flex-col gap-4">
        <h2 className="text-xl font-bold text-gray-800">Confirm Deletion</h2>
        <p className="text-gray-600">
          {customMessage ? customMessage : `Are you sure you want to delete this ${addressToDelete?.type === "shipping" ? "shipping" : "billing"} address?`}
        </p>
        <div className="flex justify-end gap-3 mt-4">
          <button
            type="button"
            className="px-4 py-2 cursor-pointer bg-gray-300 rounded-md hover:bg-gray-400 text-gray-800"
            onClick={cancelDeleteAddress}
          >
            Cancel
          </button>
          <button
            type="button"
            className="px-4 py-2 cursor-pointer bg-red-500 text-white rounded-md hover:bg-red-600"
            onClick={confirmDeleteAddress}
          >
            Confirm
          </button>
        </div>
      </div>
    </div>
  );
};

export default DeleteConfirmationModal;
