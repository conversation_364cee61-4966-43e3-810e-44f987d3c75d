import { fetchApi, getServerCookies } from "@/api/getapis";
import PatientFile from "@/components/patient-file/PatientFile";
import { PatientFileData, Specialist } from "@/types/types";
import { API_ROUTES } from "@/utils/ApiRoutes";
import React from "react";

interface PageProps {
  params: Promise<{ slug?: string }>;
  searchParams: Promise<{ id?: string }>;
}

const Page = async ({ searchParams }: PageProps) => {
  const resolvedSearchParams = await searchParams;
  const patientId = resolvedSearchParams.id ?? "";

  let patientData: PatientFileData | null = null;
  if (patientId) {
    // Determine user role to select correct API route
    const role = await getServerCookies("Role");
    console.log("🚀 ~ page ~ role:", role);
    const baseRoute =
      role !== "specialist"
        ? API_ROUTES.PATIENT.GET_PATIENT_BY_ID
        : API_ROUTES.PATIENT.GET_PATIENT_BY_ID_FOR_SPECIALIST;

    const url = `${baseRoute}/${patientId}`;
    const patientsArray = await fetchApi(url) as PatientFileData;
    const specialist = await fetchApi(`${API_ROUTES.PATIENT.GET_SPECIALIST}/${patientsArray.id}/specialist`);
    console.log("🚀 ~ page ~ specialist:", specialist);
    // console.log("🚀 ~ page ~ patientsArray:", patientsArray);
    patientData = patientsArray;
  }

  return (
    <>
      <PatientFile
        data={patientData as PatientFileData}
        patientId={patientId}
        specialist={specialist as Specialist | null}
      />
    </>
  );
};

export default Page;
