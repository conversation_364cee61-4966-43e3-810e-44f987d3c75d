import { fetchApi } from "../getapis";
import { API_ROUTES } from "@/utils/ApiRoutes";
import { AddAddressPayload, DeleteAddressPayload, UpdateAddressPayload, ApiResponse, Address } from "@/types/types";

// Server-side fetch for addresses (GET)
export const fetchAddresses = async (): Promise<Address[] | null> => {
  return await fetchApi<Address[]>(API_ROUTES.ADRESSES.GET_ADRESS);
};

export const addAddress = async (payload: AddAddressPayload): Promise<ApiResponse> => {
  try {
    const formData = new FormData();
    formData.append("clinic_name", payload.clinic_name);
    formData.append("street_address", payload.street_address);
    formData.append("city", payload.city);
    formData.append("postal_code", payload.postal_code);
    formData.append("phone_number", payload.phone_number);
    formData.append("address_type", payload.address_type);

    const res = await fetch(API_ROUTES.ADRESSES.ADD_ADDRESS, {
      method: "POST",
      headers: {
        Accept: "application/json",
        Authorization: `Bearer ${payload.token}`,
      },
      body: formData,
      credentials: "include",
    });

    const data: ApiResponse = await res.json();
    if (!res.ok) {
      throw new Error(data.message || "Failed to add address");
    }

    return data;
  } catch (error) {
    const err = error as Error;
    console.error("Error adding address:", err.message);
    throw error;
  }
};

// Delete Address helper function:
export const deleteAddress = async (payload: DeleteAddressPayload): Promise<ApiResponse> => {
  try {
    const res = await fetch(`${API_ROUTES.ADRESSES.DELETE_ADDRESS}/${payload.addressId}`, {
      method: "DELETE",
      headers: {
        Accept: "application/json",
        Authorization: `Bearer ${payload.token}`,
      },
      credentials: "include",
    });

    const data: ApiResponse = await res.json();
    if (!res.ok) {
      throw new Error(data.message || "Failed to delete address");
    }

    return data;
  } catch (error) {
    const err = error as Error;
    console.error("Error deleting address:", err.message);
    throw error;
  }
};

// Update Address helper function:
export const updateAddress = async (payload: UpdateAddressPayload): Promise<ApiResponse> => {
  try {
    const formData = new FormData();
    formData.append("clinic_name", payload.clinic_name);
    formData.append("street_address", payload.street_address);
    formData.append("city", payload.city);
    formData.append("postal_code", payload.postal_code);
    formData.append("phone_number", payload.phone_number);
    formData.append("address_type", payload.address_type);

    const res = await fetch(`${API_ROUTES.ADRESSES.UPDATE_ADDRESS}/${payload.addressId}`, {
      method: "PUT",
      headers: {
        Accept: "application/json",
        Authorization: `Bearer ${payload.token}`,
      },
      body: formData,
      credentials: "include",
    });

    const data: ApiResponse = await res.json();
    if (!res.ok) {
      throw new Error(data.message || "Failed to update address");
    }

    return data;
  } catch (error) {
    const err = error as Error;
    console.error("Error updating address:", err.message);
    throw error;
  }
};
