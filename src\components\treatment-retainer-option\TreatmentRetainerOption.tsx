'use client'
import { useRouter } from 'next/navigation';
import React, { useState } from 'react'
import FormWrapper from '../reuseable/FormWrapper';
import FormHeading from '../reuseable/FormHeading';
import CommenCard from '../reuseable/CommenCard';
import { PatientFileData, Plan } from '@/types/types';
import getAndDecryptCookie, { Encrytion, storeToken } from '@/app/lib/auth';
import { createPatient } from '@/utils/ApisHelperFunction';
import { toast } from 'react-toastify';


interface TreatmentOptionsProps {
    data: Plan[];
    patientData: PatientFileData | null;
}

const TreatmentRetainerOption = ({ data, patientData }: TreatmentOptionsProps) => {
    const router = useRouter();
    const [treatmentOption, setTreatmentOption] = useState<string>('');
    const selectedPlanId = patientData?.plan.id.toString() || "";
    // Show all plans from data prop
    const plansToShow = Array.isArray(data) ? data : [];

    const onSubmit = () => {
        console.log(treatmentOption)
    }
    const handleSelect = async (planid: string, value: string) => {
        console.log("🚀 ~ handleSelect ~ id:", planid, "value:", value);
        setTreatmentOption(value);
        try {
            const token = getAndDecryptCookie("AccessToken");
            const patientData = getAndDecryptCookie("retainerData");

            if (!token) {
                toast.error("Login required");
                return;
            }
            if (!patientData) {
                toast.error("Basic details needed");
                return;
            }

            let patient = null;
            if (patientData) {
                patient = JSON.parse(patientData);
            } else {
                throw new Error("No patient data found");
            }

            const response = await createPatient(token as string, {
                ...patient,
                planid,
            });
            console.log("🚀 ~ handleSelect ~ response:", response);

            if (response) {
                console.log("Patient created successfully:", response.data);
                localStorage.setItem("patientData", JSON.stringify(patientData));
                const patientIdentity = response?.data?.id || "";
                console.log("🚀 ~ handleSelect ~ patientIdentity:", patientIdentity);
                const patientId = Encrytion(String(patientIdentity));
                storeToken("patientId", patientId, true);
                router.push("/retainer-info")
            } else {
                console.error("Failed to create patient");
            }
        } catch (error) {
            console.error("Error during patient creation:", error);
        }

    }
    return (
        <FormWrapper classNames='!grid-cols-1' onSubmit={() => onSubmit()} onBack={() => router.back()} showNextButton={false}>
            <div className='col-span-1 flex flex-col flex-grow'>
                <FormHeading text='Select treatment plan.' classes='!text-2xl' />

                <div className='relative grid 2xl:grid-cols-3 xl:grid-cols-2 grid-cols-1 gap-4 p-8 !bg-primaryLight rounded-[10px]'>
                    {plansToShow.map((plan) => (
                        <CommenCard
                            key={plan.id}
                            classes="col-span-1"
                            header={plan.name}
                            buttonText="Select"
                            selectedValue={treatmentOption || selectedPlanId}
                            value={plan.id.toString()}
                            planid={plan.id.toString()}
                            onClickButton={handleSelect}
                            flexGrow={true}
                        >
                            <div className="">
                                <p className="text-primary font-semibold text-lg">
                                    {plan.name}
                                </p>

                                <div className="flex flex-col gap-2 mt-8 mb-2">
                                    <div className="flex justify-between gap-7">
                                        <p className="flex-grow text-dark">
                                            Duration:{" "}
                                            {plan.duration_years
                                                ? `${plan.duration_years} year(s)`
                                                : "N/A"}
                                        </p>
                                        <p className="text-dark font-bold flex items-center">
                                            Expiration:{" "}
                                            {plan.expiration_date
                                                ? new Date(plan.expiration_date).toLocaleDateString()
                                                : "N/A"}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </CommenCard>
                    ))}
                </div>
            </div>
        </FormWrapper>
    )
}

export default TreatmentRetainerOption
