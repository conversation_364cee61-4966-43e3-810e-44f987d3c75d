import { fetchApi, getServerCookies } from '@/api/getapis';
import PatientRecords from '@/components/patient-records/PatientRecords'
import { PatientFileData } from '@/types/types';
import { API_ROUTES } from '@/utils/ApiRoutes';
import React from 'react'

const page = async () => {
    const patientId = await getServerCookies("patientId");
    let patientData: PatientFileData | null = null;

    if (patientId) {
        const patientsArray = await fetchApi(`${API_ROUTES.PATIENT.GET_PATIENT_BY_ID}/${patientId}`);
        patientData = patientsArray as PatientFileData;
        console.log("🚀 ~ page ~ patientData:", patientData);
    } else {
        console.log("🚀 ~ page ~ patientData:", patientData);
    }

    return (
        <div>

            <PatientRecords patientData={patientData} />
        </div>
    )
}

export default page
