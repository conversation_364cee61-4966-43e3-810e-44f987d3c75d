import { fetchApi, getServerCookies } from '@/api/getapis';
import PatientRetainer from '@/components/retainer-components/PatientRetainer'
import { Address, PatientFileData } from '@/types/types';
import { API_ROUTES } from '@/utils/ApiRoutes';
import React from 'react'

const page = async () => {
     const data = await fetchApi(`${API_ROUTES.ADRESSES.GET_ADRESS}`) as Address[];
      const patientId = await getServerCookies("patientId");
      let patientData: PatientFileData | null = null;
    
      if (patientId) {
        const patientsArray = await fetchApi(`${API_ROUTES.PATIENT.GET_PATIENT_BY_ID}/${patientId}`);
        patientData = patientsArray as PatientFileData;
        console.log("🚀 ~ page ~ patientData:", patientData);
      } else {
        console.log("🚀 ~ page ~ patientData:", patientData);
      }
    
    return (
        <>
            <PatientRetainer  data={data}  patientData={patientData} />
        </>
    )
}

export default page
