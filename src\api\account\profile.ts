import { fetchApi } from "../getapis";

import { API_ROUTES } from "@/utils/ApiRoutes";
import { DoctorProfileApiResponse, UpdateDoctorProfilePayload } from "@/types/types";

export const fetchDoctorProfile = async (): Promise<DoctorProfileApiResponse['data'] | null> => {
  return await fetchApi<DoctorProfileApiResponse['data']>(API_ROUTES.PROFILE.GET_PROFILE);
};

export const updateDoctorProfile = async (
  token: string,
  payload: UpdateDoctorProfilePayload
): Promise<boolean> => {
  try {
    if (!token) {
      console.error('Authorization token is missing');
      return false;
    }
    const formData = new FormData();
    formData.append('first_name', payload.first_name);
    formData.append('last_name', payload.last_name);
    formData.append('email', payload.email);
    formData.append('username', payload.username);

    const response = await fetch(API_ROUTES.PROFILE.UPDATE_PROFILE, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${token}`,
        // 'Content-Type' should NOT be set when using FormData; browser will set it with boundary
      },
      body: formData,
    });
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Error updating doctor profile: ${response.statusText}`, errorText);
      return false;
    }
    return true;
  } catch (error) {
    console.error('Error updating doctor profile:', error);
    return false;
  }
};
