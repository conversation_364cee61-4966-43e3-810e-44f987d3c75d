import { AddAddressPayload, AlignerReplacementResponse, CreatePatientResponse, DeleteAddressPayload, DoctorProfileApiResponse, EmployeeApiResponse, RefinementResponse, RetainerRequestPayload, RetainerRequestResponse, SubmitRefinementPayload, UpdateDoctorProfilePayload, UploadCbctPayload, UploadCbctResponse } from "@/types/types";
import { API_ROUTES } from "./ApiRoutes";
import { ApiResponse } from "@/types/types";

// Define proper response types
interface LoginResponse extends ApiResponse {
  data?: {
    accessToken?: string;
    user?: Record<string, unknown>;
  };
}

interface ForgetPasswordResponse extends ApiResponse {
  data?: {
    message?: string;
  };
}

interface ClinicalConditionsResponse extends ApiResponse {
  data?: {
    conditions?: Record<string, unknown>;
  };
}

interface PatientDataResponse extends ApiResponse {
  data?: {
    records?: Record<string, unknown>;
  };
}

interface CasePrescriptionResponse extends ApiResponse {
  data?: {
    prescription?: Record<string, unknown>;
  };
}

export const loginUser = async (
  identifier: string,
  password: string,
  remember?: boolean
): Promise<LoginResponse | null> => {
  try {
    const formData = new FormData();
    const isEmail = identifier.includes('@') && identifier.includes('.com');

    // Add appropriate field based on identifier type
    if (isEmail) {
      formData.append('email', identifier);
    } else {
      formData.append('username', identifier);
    }

    formData.append('password', password);
    if (remember) {
      formData.append('remember', remember.toString());
    }

    const response = await fetch(`${API_ROUTES.AUTH.LOGIN}`, {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      console.error('Login failed:', response.statusText);
      return null;
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Login error:', error);
    return null;
  }
};

export const forgetPassword = async (identifier: string): Promise<ForgetPasswordResponse | null> => {
  try {
    const formData = new FormData();
    const isEmail = identifier.includes('@') && identifier.includes('.');

    if (isEmail) {
      formData.append('email', identifier);
    } else {
      formData.append('username', identifier);
    }

    const response = await fetch(`${API_ROUTES.AUTH.FORGET_PASSWORD}`, {
      method: 'POST',
      headers: {
        'accept': 'application/json'
      },
      body: formData,
    });

    if (!response.ok) {
      console.error('Forget password failed:', response.statusText);
      return null;
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Forget password error:', error);
    return null;
  }
};

export const fetchPaginatedData = async function <T>(
  url: string,
  page: number = 1,
  limit: number = 50,
  token: string
): Promise<T | null> {
  try {
    if (!token) {
      console.error("Authorization token is missing");
      return null;
    }

    if (page <= 0 || limit <= 0) {
      console.error("Invalid pagination parameters: page and limit must be greater than 0");
      return null;
    }

    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString()
    }).toString();
    const fullUrl = `${url}?${queryParams}`;

    const response = await fetch(fullUrl, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
        "Cache-Control": "no-cache",
      },
    });

    if (!response.ok) {
      console.error(`Error fetching data: ${response.statusText}`);
      return null;
    }

    const data: T = await response.json();
    if (!data) {
      console.error("Invalid response structure: data is null or undefined");
      return null;
    }

    return data;
  } catch (error) {
    console.error("Error fetching paginated data:", error);
    return null;
  }
};

export const createPatient = async (
  token: string,
  patientData: {
    planid: string,
    country: string;
    step: string;
    first_name: string;
    last_name: string;
    dob: string;
    gender: string;
    ship_to_office: string;
    bill_to_office: string;
  },
  id?: string
): Promise<CreatePatientResponse | null> => {
  try {
    if (!token) {
      console.error("Authorization token is missing");
      return null;
    }

    const formData = new FormData();
    if (id !== undefined) {
      formData.append("id", id);
    }
    formData.append("country", patientData.country);
    formData.append("step", patientData.step);
    formData.append("first_name", patientData.first_name);
    formData.append("last_name", patientData.last_name);
    formData.append("dob", patientData.dob);
    formData.append("gender", patientData.gender);
    formData.append("ship_to_office", patientData.ship_to_office);
    formData.append("bill_to_office", patientData.bill_to_office);
    formData.append("plan_id", patientData.planid);

    const response = await fetch(`${API_ROUTES.PATIENT.ADD_PATIENT}`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: formData,
    });

    if (!response.ok) {
      console.error(`Error creating patient: ${response.statusText}`);
      return null;
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error creating patient:", error);
    return null;
  }
};

export const submitClinicalConditions = async (
  token: string,
  payload: {
    step: string;
    id: string;
    clinical_conditions: string;
    general_notes: string;
  }
): Promise<ClinicalConditionsResponse | null> => {
  console.log("🚀 ~ payload:", payload)
  try {
    const formData = new FormData();
    formData.append("step", payload.step);
    formData.append("id", payload.id);
    formData.append("clinical_conditions", payload.clinical_conditions);
    formData.append("general_notes", payload.general_notes);

    const response = await fetch(API_ROUTES.PATIENT.ADD_CLINICAL_DATA, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: formData,
    });

    if (!response.ok) {
      console.error("Error submitting clinical conditions:", response.statusText);
      return null;
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error in submitClinicalConditions:", error);
    return null;
  }
};

export const submitPatientData = async (
  token: string,
  id: string,
  records: {
    stlFile1: File;
    stlFile2: File;
    cbctFile: File;
    profileRepose: File;
    buccalRight: File;
    buccalLeft: File;
    frontalRepose: File;
    frontalSmiling: File;
    labialAnterior: File;
    occlussalLower: File;
    occlussalUpper: File;
    radioGraph1?: File | null;
    radioGraph2?: File | null;
    generalRecords?: File[];
  }
): Promise<PatientDataResponse | null> => {
  const formData = new FormData();
  formData.append("step", "step3");
  formData.append("id", id);
  formData.append("stlFile1", records.stlFile1);
  formData.append("stlFile2", records.stlFile2);
  formData.append("cbctFile", records.cbctFile);
  formData.append("profileRepose", records.profileRepose);
  formData.append("buccalRight", records.buccalRight);
  formData.append("buccalLeft", records.buccalLeft);
  formData.append("frontalRepose", records.frontalRepose);
  formData.append("frontalSmiling", records.frontalSmiling);
  formData.append("labialAnterior", records.labialAnterior);
  formData.append("occlussalLower", records.occlussalLower);
  formData.append("occlussalUpper", records.occlussalUpper);
  formData.append("radioGraph1", records.radioGraph1 || new Blob());
  formData.append("radioGraph2", records.radioGraph2 || new Blob());

  // if (records.generalRecords && records.generalRecords.length > 0) {
  //   records.generalRecords.forEach((file, index) => {
  //     formData.append(`generalRecords[${index}]`, file);
  //   });
  // }

  try {
    const response = await fetch(API_ROUTES.PATIENT.ADD_RECORDS_DATA, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: formData,
    });

    if (!response.ok) {
      console.error("Error submitting patient data:", response.statusText);
      return null;
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error in submitPatientData:", error);
    return null;
  }
};

export const submitCasePrescription = async (
  token: string,
  id: string,
  casePrescriptionData: Record<string, unknown>,
  version: string
): Promise<CasePrescriptionResponse | null> => {
  console.log("🚀 ~ version:", version)
  try {
    if (!token) {
      console.error("Authorization token is missing");
      return null;
    }

    const formData = new FormData();
    formData.append("step", "step4");
    formData.append("id", id);
    formData.append("case_prescription", JSON.stringify(casePrescriptionData));
    // formData.append("version", version);

    const response = await fetch(API_ROUTES.PATIENT.ADD_CASE_PERSCRIPTION, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: formData,
    });

    if (!response.ok) {
      console.error("Error submitting case prescription:", response.statusText);
      return null;
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error in submitCasePrescription:", error);
    return null;
  }
};

// Fetch employees/staff data from API
export const fetchEmployees = async (
  token: string,
  page: number = 1,
  limit: number = 10
): Promise<{
  employees: unknown[];
  currentPage: number;
  perPage: number;
  totalItems: number;
  totalPages: number;
} | null> => {
  try {
    if (!token) {
      console.error("Authorization token is missing");
      return null;
    }
    const response = await fetch(`${API_ROUTES.EMPLOYEE.GET_EMPLOYEES}?page=${page}&limit=${limit}`,
      {
        method: 'GET',
        headers: {
          'accept': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      }
    );
    if (!response.ok) {
      console.error(`Error fetching employees: ${response.statusText}`);
      return null;
    }
    const data = await response.json();
    // Adjust for nested data structure
    const d = data.data || {};
    return {
      employees: d.data || [],
      currentPage: d.currentPage || 1,
      perPage: d.perPage || 10,
      totalItems: d.totalItems || 0,
      totalPages: d.totalPages || 1,
    };
  } catch (error) {
    console.error('Error fetching employees:', error);
    return null;
  }
};

// Update employee (edit)
export const updateEmployee = async (
  employeeId: string,
  data: {
    first_name: string;
    last_name: string;
    email: string;
    salutation: string;
    practice_phone_number: string;
    mobile: string;
    profession: string;
  },
  token: string
): Promise<unknown> => {
  try {
    if (!token) {
      throw new Error('Authorization token is missing');
    }
    const formData = new FormData();
    formData.append('first_name', data.first_name);
    formData.append('last_name', data.last_name);
    formData.append('email', data.email);
    formData.append('salutation', data.salutation);
    formData.append('practice_phone_number', data.practice_phone_number);
    formData.append('mobile', data.mobile);
    formData.append('profession', data.profession);

    const response = await fetch(`${API_ROUTES.EMPLOYEE.GET_EMPLOYEES}/${employeeId}`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${token}`,
        'accept': 'application/json',
      },
      body: formData,
    });
    if (!response.ok) {
      let errorMsg = `Error updating employee: ${response.statusText}`;
      try {
        const errorData = await response.json();
        if (errorData && errorData.message) {
          errorMsg = errorData.message;
        }
      } catch { }
      throw new Error(errorMsg);
    }
    const result = await response.json();
    return result;
  } catch (error) {
    throw error;
  }
};

// Create employee (add)
export const createEmployee = async (
  data: {
    first_name: string;
    last_name: string;
    email: string;
    salutation: string;
    practice_phone_number: string;
    mobile: string;
    profession: string;
  },
  token: string
): Promise<unknown> => {
  try {
    if (!token) {
      throw new Error('Authorization token is missing');
    }
    const formData = new FormData();
    formData.append('first_name', data.first_name);
    formData.append('last_name', data.last_name);
    formData.append('email', data.email);
    formData.append('salutation', data.salutation);
    formData.append('practice_phone_number', data.practice_phone_number);
    formData.append('mobile', data.mobile);
    formData.append('profession', data.profession);

    // Debug: log FormData
    // console.log('createEmployee called');
    // for (let pair of formData.entries()) {
    //   console.log('FormData:', pair[0], pair[1]);
    // }

    const response = await fetch(API_ROUTES.EMPLOYEE.CREATE_EMPLOYEE, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'accept': 'application/json',
      },
      body: formData,
    });
    if (!response.ok) {
      let errorMsg = `Error creating employee: ${response.statusText}`;
      try {
        const errorData = await response.json();
        if (errorData && errorData.message) {
          errorMsg = errorData.message;
        }
      } catch { }
      throw new Error(errorMsg);
    }
    const result = await response.json();
    return result;
  } catch (error) {
    throw error;
  }
};

// Update employee status (active/inactive)
export const updateEmployeeStatus = async (
  employeeId: string,
  status: 'active' | 'inactive',
  token: string
): Promise<unknown> => {
  try {
    if (!token) {
      throw new Error('Authorization token is missing');
    }
    const body = new URLSearchParams();
    body.append('status', status);
    const response = await fetch(`${API_ROUTES.EMPLOYEE.GET_EMPLOYEES}/${employeeId}`, {
      method: 'PATCH',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/x-www-form-urlencoded',
        'accept': 'application/json',
      },
      body,
    });
    if (!response.ok) {
      let errorMsg = `Error updating employee status: ${response.statusText}`;
      try {
        const errorData = await response.json();
        if (errorData && errorData.message) {
          errorMsg = errorData.message;
        }
      } catch { }
      throw new Error(errorMsg);
    }
    const result = await response.json();
    return result;
  } catch (error) {
    throw error;
  }
};


export const fetchEmployeeById = async (
  id: string | number,
  token: string
): Promise<EmployeeApiResponse['data'] | null> => {
  try {
    if (!token) {
      console.error('Authorization token is missing');
      return null;
    }
    const response = await fetch(`${API_ROUTES.EMPLOYEE.GET_EMPLOYEE_BY_ID}/${id}`, {
      method: 'GET',
      headers: {
        'accept': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
    });
    if (!response.ok) {
      console.error(`Error fetching employee: ${response.statusText}`);
      return null;
    }
    const data: EmployeeApiResponse = await response.json();
    return data.data;
  } catch (error) {
    console.error('Error fetching employee:', error);
    return null;
  }
};



// API function for creating patient
export const createRetainerPatient = async (
  token: string,
  patientData: {
    planid: string;
    country: string;
    step: string;
    first_name: string;
    last_name: string;
    dob: string;
    gender: string;
    ship_to_office: string;
    bill_to_office: string;
  }
) => {
  try {
    const formData = new FormData();

    // Add all required fields to FormData
    formData.append('step', 'step1');
    formData.append('first_name', patientData.first_name);
    formData.append('last_name', patientData.last_name);
    formData.append('dob', patientData.dob);
    formData.append("plan_id", patientData.planid);
    formData.append('gender', patientData.gender);
    formData.append('ship_to_office', patientData.ship_to_office);
    formData.append('bill_to_office', patientData.bill_to_office);

    const response = await fetch(API_ROUTES.RETAINER.CREATE_RETAINER_PATIENT, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
      },
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to create patient');
    }
    return await response.json();
  } catch (error) {
    console.error('API Error:', error);
    throw error;
  }
};// Add Address helper function:


export const addAddress = async (payload: AddAddressPayload): Promise<ApiResponse> => {
  try {
    const formData = new FormData();
    formData.append("clinic_name", payload.clinic_name);
    formData.append("street_address", payload.street_address);
    formData.append("city", payload.city);
    formData.append("postal_code", payload.postal_code);
    formData.append("phone_number", payload.phone_number);
    formData.append("address_type", payload.address_type);

    const res = await fetch(API_ROUTES.ADRESSES.ADD_ADDRESS, {
      method: "POST",
      headers: {
        Accept: "application/json",
        Authorization: `Bearer ${payload.token}`,
      },
      body: formData,
      credentials: "include",
    });

    const data: ApiResponse = await res.json();
    if (!res.ok) {
      throw new Error(data.message || "Failed to add address");
    }

    return data;
  } catch (error) {
    const err = error as Error;
    console.error("Error adding address:", err.message);
    throw error;
  }
};


// Delete Address helper function:


export const deleteAddress = async (payload: DeleteAddressPayload): Promise<ApiResponse> => {
  try {
    const res = await fetch(`${API_ROUTES.ADRESSES.DELETE_ADDRESS}/${payload.addressId}`, {
      method: "DELETE",
      headers: {
        Accept: "application/json",
        Authorization: `Bearer ${payload.token}`,
      },
      credentials: "include",
    });

    const data: ApiResponse = await res.json();
    if (!res.ok) {
      throw new Error(data.message || "Failed to delete address");
    }

    return data;
  } catch (error) {
    const err = error as Error;
    console.error("Error deleting address:", err.message);
    throw error;
  }
};

// Update Address helper function:
interface UpdateAddressPayload {
  addressId: string | number;
  clinic_name: string;
  street_address: string;
  city: string;
  postal_code: string;
  phone_number: string;
  address_type: "bill_to" | "ship_to";
  token: string; // JWT token from localStorage or wherever it's stored
}

export const updateAddress = async (payload: UpdateAddressPayload): Promise<ApiResponse> => {
  try {
    const formData = new FormData();
    formData.append("clinic_name", payload.clinic_name);
    formData.append("street_address", payload.street_address);
    formData.append("city", payload.city);
    formData.append("postal_code", payload.postal_code);
    formData.append("phone_number", payload.phone_number);
    formData.append("address_type", payload.address_type);

    const res = await fetch(`${API_ROUTES.ADRESSES.UPDATE_ADDRESS}/${payload.addressId}`, {
      method: "PUT",
      headers: {
        Accept: "application/json",
        Authorization: `Bearer ${payload.token}`,
      },
      body: formData,
      credentials: "include",
    });

    const data: ApiResponse = await res.json();
    if (!res.ok) {
      throw new Error(data.message || "Failed to update address");
    }

    return data;
  } catch (error) {
    const err = error as Error;
    console.error("Error updating address:", err.message);
    throw error;
  }
};



export const fetchDoctorProfile = async (
  token: string
): Promise<DoctorProfileApiResponse['data'] | null> => {
  try {
    if (!token) {
      console.error('Authorization token is missing');
      return null;
    }
    const response = await fetch(API_ROUTES.PROFILE.GET_PROFILE, {
      method: 'GET',
      headers: {
        'accept': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
    });
    if (!response.ok) {
      console.error(`Error fetching doctor profile: ${response.statusText}`);
      return null;
    }
    const data: DoctorProfileApiResponse = await response.json();
    return data.data;
  } catch (error) {
    console.error('Error fetching doctor profile:', error);
    return null;
  }
};


export const updateDoctorProfile = async (
  token: string,
  payload: UpdateDoctorProfilePayload
): Promise<boolean> => {
  try {
    if (!token) {
      console.error('Authorization token is missing');
      return false;
    }
    const formData = new FormData();
    formData.append('first_name', payload.first_name);
    formData.append('last_name', payload.last_name);
    formData.append('email', payload.email);
    formData.append('username', payload.username);

    const response = await fetch(API_ROUTES.PROFILE.GET_PROFILE, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${token}`,
      },
      body: formData,
    });
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Error updating doctor profile: ${response.statusText}`, errorText);
      return false;
    }
    return true;
  } catch (error) {
    console.error('Error updating doctor profile:', error);
    return false;
  }
};


export const submitRefinementRequest = async (
  patientId: string | number,
  token: string,
  payload: SubmitRefinementPayload
): Promise<RefinementResponse | null> => {
  try {
    const formData = new FormData();
    formData.append('refinementDetails', payload.refinementDetails);
    formData.append('upperImpression', payload.upperImpression);
    formData.append('lowerImpression', payload.lowerImpression);
    formData.append('profileRepose', payload.profileRepose);
    formData.append('buccalRight', payload.buccalRight);
    formData.append('buccalLeft', payload.buccalLeft);
    formData.append('frontalRepose', payload.frontalRepose);
    formData.append('frontalSmiling', payload.frontalSmiling);
    formData.append('labialAnterior', payload.labialAnterior);
    formData.append('occlussalLower', payload.occlussalLower);
    formData.append('occlussalUpper', payload.occlussalUpper);
    formData.append('radioGraph1', payload.radioGraph1);
    formData.append('radioGraph2', payload.radioGraph2);

    const response = await fetch(
      `${API_ROUTES.PATIENT.ADD_REFINEMENT_ALIGNER}/${patientId}/refinements`,
      {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
        },
        body: formData,
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Error submitting refinement: ${response.statusText}`, errorText);
      return null;
    }

    const data: RefinementResponse = await response.json();
    return data;
  } catch (error) {
    console.error('Error submitting refinement:', error);
    return null;
  }
};



export const submitAlignerReplacement = async (
  token: string,
  replacementData: string,
  patientId: string | number
): Promise<AlignerReplacementResponse | null> => {
  try {
    const params = new URLSearchParams();
    params.append('replacement_data', replacementData);
    params.append('patient_id', String(patientId));

    const response = await fetch(API_ROUTES.PATIENT.ADD_REPLACEMENT_ALIGNER, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Bearer ${token}`,
      },
      body: params.toString(),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Error submitting aligner replacement: ${response.statusText}`, errorText);
      return null;
    }

    const data: AlignerReplacementResponse = await response.json();
    return data;
  } catch (error) {
    console.error('Error submitting aligner replacement:', error);
    return null;
  }
};



export const submitRetainerRequest = async (
  token: string,
  payload: RetainerRequestPayload
): Promise<RetainerRequestResponse | null> => {
  try {
    const formData = new FormData();
    formData.append("patient_id", String(payload.patient_id));
    formData.append("mode", payload.mode);

    // Only append other_details if it exists and is not undefined/null
    if (payload.other_details) {
      formData.append("other_details", payload.other_details);
    }

    // Only append files if they exist and are not null
    if (payload.stlFile1) formData.append("stlFile1", payload.stlFile1);
    if (payload.stlFile2) formData.append("stlFile2", payload.stlFile2);
    if (payload.cbctFile) formData.append("cbctFile", payload.cbctFile);
    if (payload.profileRepose) formData.append("profileRepose", payload.profileRepose);
    if (payload.buccalRight) formData.append("buccalRight", payload.buccalRight);
    if (payload.buccalLeft) formData.append("buccalLeft", payload.buccalLeft);
    if (payload.frontalRepose) formData.append("frontalRepose", payload.frontalRepose);
    if (payload.frontalSmiling) formData.append("frontalSmiling", payload.frontalSmiling);
    if (payload.labialAnterior) formData.append("labialAnterior", payload.labialAnterior);
    if (payload.occlusalLower) formData.append("occlussalLower", payload.occlusalLower);
    if (payload.occlusalUpper) formData.append("occlussalUpper", payload.occlusalUpper);
    if (payload.radioGraph1) formData.append("radioGraph1", payload.radioGraph1);
    if (payload.radioGraph2) formData.append("radioGraph2", payload.radioGraph2);

    const response = await fetch(
      API_ROUTES.PATIENT.ADD_4DGRAPHY_RETAINER,
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
        },
        body: formData,
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Error submitting retainer request: ${response.statusText}`, errorText);
      return null;
    }

    const data: RetainerRequestResponse = await response.json();
    return data;
  } catch (error) {
    console.error("Error submitting retainer request:", error);
    return null;
  }
};



export const uploadCbctFile = async (
  token: string,
  payload: UploadCbctPayload
): Promise<UploadCbctResponse | null> => {
  try {
    const formData = new FormData();
    formData.append("cbctFile", payload.cbctFile);
    formData.append("reason", payload.reason);

    const response = await fetch(

      `${API_ROUTES.PATIENT.ADD_CBCT_FILE}/${payload.patientId}/cbct`,
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
        },
        body: formData,
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Error uploading CBCT file: ${response.statusText}`, errorText);
      return null;
    }

    const data: UploadCbctResponse = await response.json();
    return data;
  } catch (error) {
    console.error("Error uploading CBCT file:", error);
    return null;
  }
};