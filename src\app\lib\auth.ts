import Cookies from 'js-cookie'
import crypto from "crypto-js";


export function storeToken(value: string, token: string, rememberMe: boolean = false) {
  Cookies.set(value, token, { 
    // If rememberMe is true, set to 7 days, otherwise 1 hour (1/24 day)
    expires: rememberMe ? 10 : 1,
    path: '/'
  });
}

 
export function getToken(value: string) {
  return Cookies.get(value)
}


const cookiesToClear = [
  'AccessToken', 'Email', 'Role', 'first_name','last_name', 'user_uuid','username', 'patientData', 'patientId', 'LongcasePrescriptionData', 'ShortcasePrescriptionData', 'retainerData'
];

export function clearAllCookies() {
  cookiesToClear.forEach(cookieName => {
    if (Cookies.get(cookieName)) {
      Cookies.remove(cookieName, {
        path: '/',
        maxAge: 0,
      });
    }
  });
}

// Get encryption key from environment variable
const ENCRYPTION_KEY = process.env.NEXT_PUBLIC_ENCRYPTION_KEY || "p7@Xr9k*2Mz#QuvL!e4C$0dT&gFnJqBs";

export const Encrytion = (value: string) => {
  const encryptedValue = crypto.AES.encrypt(
    value,
    ENCRYPTION_KEY
  ).toString();
  return encryptedValue;
}

export const Decrytion = (value: string) => {
  const decryptedValue = crypto.AES.decrypt(
    value,
    ENCRYPTION_KEY
  ).toString(crypto.enc.Utf8)
  return decryptedValue;
}


const getAndDecryptCookie = (cookieName: string) => {
  const encryptedValue = Cookies.get(cookieName);
  if (encryptedValue) {
    const decryptedValue = Decrytion(encryptedValue);
    return decryptedValue;
  }
  return null;
};

export function removeToken(key: string): void {
  Cookies.remove(key);
}
export function logout(): void {
  clearAllCookies();
  window.location.href = '/login';
}
export default getAndDecryptCookie;