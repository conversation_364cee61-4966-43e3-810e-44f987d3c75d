"use client";

import React, { useState, useEffect } from "react";
import { toast } from "react-toastify";
import { Address } from "@/types/types";
import getAndDecryptCookie from "@/app/lib/auth";
import { DoctorAddress, fetchDoctorAddresses } from "@/api/account/doctorAddresses";
import {
  fetchDoctorProfile,
  updateDoctorProfile,
} from "@/api/account/profile";
import {
  addAddress,
  deleteAddress,
  updateAddress,
} from "@/api/account/addresses";
import AddressModal from "./modals/AddressModal";
import DeleteConfirmationModal from "@/components/reuseable/DeleteConfirmationModal";

type ModalAddress = {
  id?: string | number;
  clinic_name: string;
  street_address: string;
  city: string;
  postal_code: string;
  phone_number: string;
  address_type: string;
};

const initialModalAddress: ModalAddress = {
  id: '',
  clinic_name: "",
  street_address: "",
  city: "",
  postal_code: "",
  phone_number: "",
  address_type: "ship_to",
};

const DrProfile: React.FC = () => {
  // State for user information
  const [username, setUsername] = useState("");
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [password] = useState("*************");
  const [email, setEmail] = useState("");
  const [isEditingUsername, setIsEditingUsername] = useState(false);
  const [isEditingFirstName, setIsEditingFirstName] = useState(false);
  const [isEditingLastName, setIsEditingLastName] = useState(false);
  const [isEditingEmail, setIsEditingEmail] = useState(false);
  const [newUsername, setNewUsername] = useState("");
  const [newFirstName, setNewFirstName] = useState("");
  const [newLastName, setNewLastName] = useState("");
  const [newEmail, setNewEmail] = useState("");
  // Loading and error state for profile
  const [profileLoading, setProfileLoading] = useState(true);
  const [profileError, setProfileError] = useState<string | null>(null);
  const [updateLoading, setUpdateLoading] = useState(false);

  // State for notification preferences
  const [notifications, setNotifications] = useState(true);
  const [isEditingNotifications, setIsEditingNotifications] = useState(false);

  const [shippingAddresses, setShippingAddresses] = useState<Address[]>([]);
  const [billingAddresses, setBillingAddresses] = useState<Address[]>([]);
  const [addresses, setAddresses] = useState<Address[]>([]);

  // Address Modal State
  const [addressModalOpen, setAddressModalOpen] = useState(false);
  const [addressModalType, setAddressModalType] = useState<
    "add" | "edit" | null
  >(null);
  const [addressType, setAddressType] = useState<"ship_to" | "bill_to">(
    "ship_to"
  );
  const [addressModalData, setAddressModalData] = useState<ModalAddress>({
    ...initialModalAddress,
  });

  // Confirmation Modal State
  const [confirmModalOpen, setConfirmModalOpen] = useState(false);
  const [addressToDelete, setAddressToDelete] = useState<{
    type: "shipping" | "billing";
    id: string;
  } | null>(null);

  // Add state for default address IDs
  const [defaultShippingId, setDefaultShippingId] = useState<number | null>(
    null
  );
  const [defaultBillingId, setDefaultBillingId] = useState<number | null>(null);

  // Refactor: loadAddresses function
  const loadAddresses = async () => {
    const token = getAndDecryptCookie("AccessToken");
    if (token) {
      const data = await fetchDoctorAddresses();
      if (data) {
        // Map DoctorAddress[] to Address[]
        const mapped: Address[] = data.map((addr: DoctorAddress) => ({
          id: Number(addr.id),
          clinic_name: addr.clinic_name,
          street_address: addr.street_address,
          city: addr.city,
          postal_code: addr.postal_code,
          phone_number: addr.phone_number,
          address_type: addr.address_type,
        }));
        setAddresses(mapped);
        // Debug log: show all addresses and their address_type
        console.log("Fetched addresses:", mapped);
        console.log(
          "address_type values:",
          mapped.map((a) => a.address_type)
        );
      }
    }
  };

  // Fetch doctor profile on mount
  useEffect(() => {
    const fetchProfile = async () => {
      setProfileLoading(true);
      setProfileError(null);
      const token = getAndDecryptCookie("AccessToken");
      if (token) {
        await fetchDoctorAddresses();
        // No need to check for token for fetchDoctorAddresses, as it is handled server-side
        const profile = await fetchDoctorProfile();
        if (profile) {
          setUsername(profile.username || "");
          setFirstName(profile.first_name || "");
          setLastName(profile.last_name || "");
          setEmail(profile.email || "");
        } else {
          setProfileError("Failed to fetch doctor profile.");
          toast.error("Failed to fetch doctor profile.");
        }
        setProfileLoading(false);
      }
    };
    fetchProfile();
  }, []);

  useEffect(() => {
    loadAddresses();
  }, []);

  // Update splitting logic to use addresses state
  useEffect(() => {
    if (addresses) {
      const shippingAdrs = addresses.filter(
        (addr) =>
          addr.address_type &&
          addr.address_type.trim().toLowerCase() === "ship_to"
      );
      const billingAdrs = addresses.filter(
        (addr) =>
          addr.address_type &&
          addr.address_type.trim().toLowerCase() === "bill_to"
      );
      setShippingAddresses(shippingAdrs);
      setBillingAddresses(billingAdrs);
      // Debug log: show filtered shipping and billing addresses
      console.log("shippingAdrs:", shippingAdrs);
      console.log("billingAdrs:", billingAdrs);
    }
  }, [addresses]);

  // When addresses change, set default IDs to first address if not already set
  useEffect(() => {
    if (shippingAddresses.length > 0 && defaultShippingId === null) {
      setDefaultShippingId(shippingAddresses[0].id);
    }
    if (billingAddresses.length > 0 && defaultBillingId === null) {
      setDefaultBillingId(billingAddresses[0].id);
    }
  }, [shippingAddresses, billingAddresses]);

  // Functions to handle editing
  const handleChangeUsername = () => {
    if (isEditingUsername) {
      if (newUsername.trim()) {
        setUsername(newUsername);
      }
      setIsEditingUsername(false);
    } else {
      setNewUsername(username);
      setIsEditingUsername(true);
    }
  };

  const handleChangeEmail = () => {
    if (isEditingEmail) {
      if (newEmail.trim()) {
        setEmail(newEmail);
      }
      setIsEditingEmail(false);
    } else {
      setNewEmail(email);
      setIsEditingEmail(true);
    }
  };

  const handleChangeNotifications = () => {
    setIsEditingNotifications(!isEditingNotifications);
  };

  const toggleNotifications = () => {
    setNotifications(!notifications);
  };

  // Add handlers for editing first and last name
  const handleChangeFirstName = () => {
    if (isEditingFirstName) {
      if (newFirstName.trim()) {
        setFirstName(newFirstName);
      }
      setIsEditingFirstName(false);
    } else {
      setNewFirstName(firstName);
      setIsEditingFirstName(true);
    }
  };
  const handleChangeLastName = () => {
    if (isEditingLastName) {
      if (newLastName.trim()) {
        setLastName(newLastName);
      }
      setIsEditingLastName(false);
    } else {
      setNewLastName(lastName);
      setIsEditingLastName(true);
    }
  };

  // Update profile handler
  const handleUpdateProfile = async (e: React.FormEvent) => {
    e.preventDefault();
    // Validate required fields
    const updatedFirstName = isEditingFirstName ? newFirstName : firstName;
    const updatedLastName = isEditingLastName ? newLastName : lastName;
    const updatedUsername = isEditingUsername ? newUsername : username;
    const updatedEmail = isEditingEmail ? newEmail : email;
    if (
      !updatedFirstName.trim() ||
      !updatedLastName.trim() ||
      !updatedUsername.trim() ||
      !updatedEmail.trim()
    ) {
      toast.error("All fields are required.");
      return;
    }
    setUpdateLoading(true);
    const token = getAndDecryptCookie("AccessToken");
    if (!token) {
      toast.error("Authentication token missing. Please log in again.");
      setUpdateLoading(false);
      return;
    }
    const success = await updateDoctorProfile(token, {
      first_name: updatedFirstName,
      last_name: updatedLastName,
      email: updatedEmail,
      username: updatedUsername,
    });
    if (success) {
      setFirstName(updatedFirstName);
      setLastName(updatedLastName);
      setUsername(updatedUsername);
      setEmail(updatedEmail);
      setIsEditingFirstName(false);
      setIsEditingLastName(false);
      setIsEditingUsername(false);
      setIsEditingEmail(false);
      toast.success("Profile updated successfully!");
    } else {
      toast.error("Failed to update profile. Please try again.");
    }
    setUpdateLoading(false);
  };

  // Functions to handle address management
  // When opening the modal for add/edit, map Address to ModalAddress
  const handleAddAddress = (type: "ship_to" | "bill_to") => {
    setAddressModalType("add");
    setAddressType(type);
    setAddressModalData({ ...initialModalAddress, address_type: type });
    setAddressModalOpen(true);
  };

  // Update handleEditAddress to use correct type
  const handleEditAddress = (addr: Address) => {
    setAddressModalType("edit");
    setAddressType(addr.address_type as "ship_to" | "bill_to");
    setAddressModalData({
      id: addr.id,
      clinic_name: addr.clinic_name,
      street_address: addr.street_address,
      city: addr.city,
      postal_code: addr.postal_code,
      phone_number: addr.phone_number,
      address_type: addr.address_type,
    });
    setAddressModalOpen(true);
  };

  const handleAddressChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setAddressModalData({
      ...addressModalData,
      [e.target.name]: e.target.value,
    });
  };

  // Update handleAddressSubmit to ensure token is string
  const handleAddressSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const token = getAndDecryptCookie("AccessToken") || "";
    const { id, clinic_name, street_address, city, postal_code, phone_number } = addressModalData;

    if (addressModalType === "add") {
      addAddress({
        clinic_name: clinic_name,
        street_address: street_address,
        city,
        postal_code,
        phone_number: phone_number,
        address_type: addressType,
        token,
      }).then(() => {
        // You might want to refetch the addresses here instead of manually adding them
        toast.success(
          `${
            addressType === "ship_to" ? "Shipping" : "Billing"
          } address added successfully!`
        );
        loadAddresses(); // Refresh addresses after add
      });
    } else if (addressModalType === "edit") {
      updateAddress({
        addressId: id ?? '',
        clinic_name: clinic_name,
        street_address: street_address,
        city,
        postal_code,
        phone_number: phone_number,
        address_type: addressType,
        token,
      }).then(() => {
        // You might want to refetch the addresses here
        toast.success(
          `${
            addressType === "ship_to" ? "Shipping" : "Billing"
          } address updated successfully!`
        );
        loadAddresses(); // Refresh addresses after edit
      });
    }
    setAddressModalOpen(false);
  };

  // Update setDefaultAddress to use number for id
  const setDefaultAddress = (id: number, type: "ship_to" | "bill_to") => {
    if (type === "ship_to") {
      setDefaultShippingId(id);
    } else {
      setDefaultBillingId(id);
    }
  };

  // Delete handlers
  // Update handleDeleteAddress to use number for id
  const handleDeleteAddress = (type: "shipping" | "billing", id: number) => {
    setAddressToDelete({ type, id: id.toString() });
    setConfirmModalOpen(true);
  };

  // Update confirmDeleteAddress to use number for id
  const confirmDeleteAddress = () => {
    if (!addressToDelete) return;

    const { type, id } = addressToDelete;
    const token = getAndDecryptCookie("AccessToken");

    deleteAddress({ addressId: id, token: token || "" }).then(() => {
      if (type === "shipping") {
        setShippingAddresses(
          shippingAddresses.filter((address) => address.id !== Number(id))
        );
      } else {
        setBillingAddresses(
          billingAddresses.filter((address) => address.id !== Number(id))
        );
      }
      toast.success(
        `${
          type === "shipping" ? "Shipping" : "Billing"
        } address deleted successfully`
      );
      loadAddresses(); // Refresh addresses after delete
    });

    setConfirmModalOpen(false);
    setAddressToDelete(null);
  };

  const cancelDeleteAddress = () => {
    setConfirmModalOpen(false);
    setAddressToDelete(null);
  };

  return (
    <>
      <div className="bg-white p-6 rounded-lg shadow-sm">
        {profileLoading ? (
          <div className="flex justify-center items-center min-h-[200px]">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          </div>
        ) : profileError ? (
          <div className="text-red-500 text-center py-4">{profileError}</div>
        ) : (
          <form
            onSubmit={handleUpdateProfile}
            className="grid grid-cols-1 md:grid-cols-2 gap-8"
          >
            {/* User Information Section */}
            <div className="space-y-6">
              <h2 className="text-xl font-bold text-gray-800 mb-4">Profile</h2>
              {/* First Name */}
              <div className="flex justify-between items-center border-b-[1px] border-gray-200 pb-2">
                <div>
                  <p className="font-medium text-gray-700">First Name</p>
                  {isEditingFirstName ? (
                    <input
                      type="text"
                      value={newFirstName}
                      onChange={(e) => setNewFirstName(e.target.value)}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500"
                      placeholder="Enter first name"
                      title="First Name"
                    />
                  ) : (
                    <p className="text-gray-600">{firstName}</p>
                  )}
                </div>
                <button
                  type="button"
                  onClick={handleChangeFirstName}
                  className="bg-[#EB6309] text-white px-4 py-1.5 rounded-full hover:bg-[#D45A08] transition-colors cursor-pointer"
                >
                  {isEditingFirstName ? "Save" : "Change"}
                </button>
              </div>
              {/* Last Name */}
              <div className="flex justify-between items-center border-b-[1px] border-gray-200 pb-2">
                <div>
                  <p className="font-medium text-gray-700">Last Name</p>
                  {isEditingLastName ? (
                    <input
                      type="text"
                      value={newLastName}
                      onChange={(e) => setNewLastName(e.target.value)}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500"
                      placeholder="Enter last name"
                      title="Last Name"
                    />
                  ) : (
                    <p className="text-gray-600">{lastName}</p>
                  )}
                </div>
                <button
                  type="button"
                  onClick={handleChangeLastName}
                  className="bg-[#EB6309] text-white px-4 py-1.5 rounded-full hover:bg-[#D45A08] transition-colors cursor-pointer"
                >
                  {isEditingLastName ? "Save" : "Change"}
                </button>
              </div>
              {/* Username */}
              <div className="flex justify-between items-center border-b-[1px] border-gray-200 pb-2">
                <div>
                  <p className=" font-medium text-gray-700">Username</p>
                  {isEditingUsername ? (
                    <input
                      type="text"
                      value={newUsername}
                      onChange={(e) => setNewUsername(e.target.value)}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500"
                      placeholder="Enter username"
                      title="Username"
                    />
                  ) : (
                    <p className=" text-gray-600">{username}</p>
                  )}
                </div>
                <button
                  type="button"
                  onClick={handleChangeUsername}
                  className="bg-[#EB6309] text-white px-4 py-1.5 rounded-full hover:bg-[#D45A08] transition-colors  cursor-pointer"
                >
                  {isEditingUsername ? "Save" : "Change"}
                </button>
              </div>
              {/* Password (not editable) */}
              <div className="flex justify-between items-center border-b-[1px] border-gray-200 pb-2">
                <div>
                  <p className="font-medium text-gray-700">Password</p>
                  <p className="text-gray-600">{password}</p>
                </div>
              </div>
              {/* Primary Account Email */}
              <div className="flex justify-between items-center border-b-[1px] border-gray-200 pb-2">
                <div>
                  <p className="font-medium text-gray-700">
                    Primary Account Email*
                  </p>
                  {isEditingEmail ? (
                    <input
                      type="email"
                      value={newEmail}
                      onChange={(e) => setNewEmail(e.target.value)}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500"
                      placeholder="Enter email"
                      title="Email"
                    />
                  ) : (
                    <p className="text-gray-600">{email}</p>
                  )}
                </div>
                <button
                  type="button"
                  onClick={handleChangeEmail}
                  className="bg-[#EB6309] text-white px-4 py-1.5 rounded-full hover:bg-[#D45A08] transition-colors cursor-pointer"
                >
                  {isEditingEmail ? "Save" : "Change"}
                </button>
              </div>
              {/* Notification Alerts */}
              <div className="flex justify-between items-center border-b-[1px] border-gray-200 pb-2">
                <div>
                  <p className="font-medium text-gray-700">Notification alerts</p>
                  {isEditingNotifications && (
                    <div className="mt-1 flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="notifications"
                        checked={notifications}
                        onChange={toggleNotifications}
                        className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
                      />
                      <label htmlFor="notifications" className="text-gray-600">
                        Receive email notifications
                      </label>
                    </div>
                  )}
                </div>
                <button
                  type="button"
                  onClick={handleChangeNotifications}
                  className="bg-[#EB6309] text-white px-4 py-1.5 rounded-full hover:bg-[#D45A08] transition-colors cursor-pointer"
                >
                  {isEditingNotifications ? "Save" : "Change"}
                </button>
              </div>
              {/* Save Button */}
              <div className="flex justify-end mt-8">
                <button
                  type="submit"
                  className="bg-[#EB6309] text-white px-6 py-2 rounded-full hover:bg-[#D45A08] transition-colors cursor-pointer disabled:opacity-60"
                  disabled={updateLoading}
                >
                  {updateLoading ? "Saving..." : "Save"}
                </button>
              </div>
            </div>
            {/* Address Management Section */}
            <div>
              {/* Default Shipping Address */}
              <div>
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium text-gray-800">
                    Default shipping address
                  </h3>
                  <button
                    type="button"
                    onClick={() => handleAddAddress("ship_to")}
                    className="bg-[#EB6309] text-white px-3 py-1.5 rounded-full hover:bg-[#D45A08] transition-colors cursor-pointer"
                  >
                    Add New
                  </button>
                </div>

                {/* Shipping Address List */}
                <div className="space-y-3">
                  {shippingAddresses.map((address) => (
                    <div
                      key={address.id}
                      className="border border-gray-200 rounded-md p-3 relative"
                    >
                      <div className="flex items-start">
                        <div className="mr-3 mt-1">
                          <input
                            type="radio"
                            name="default-shipping"
                            checked={address.id === defaultShippingId}
                            onChange={() =>
                              setDefaultAddress(address.id, "ship_to")
                            }
                            className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300"
                            title="Select as default shipping address"
                          />
                        </div>
                        <div className="w-full">
                          <p className="font-medium">{address.clinic_name}</p>
                          <p className="text-gray-600">{`${address.street_address}, (#${address.postal_code}), ${address.city}`}</p>
                          <p className="text-gray-600">{address.phone_number}</p>
                        </div>
                        <div className="flex space-x-2 ml-2">
                          <button
                            onClick={() =>
                              handleDeleteAddress("shipping", address.id)
                            }
                            className="text-red-500 cursor-pointer"
                            title="Delete shipping address"
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="24"
                              height="24"
                              viewBox="0 0 24 24"
                              fill="none"
                            >
                              <path
                                d="M10.2499 2.00006C10.1291 1.99851 10.0098 2.02617 9.90195 2.08067C9.79413 2.13517 9.70108 2.2149 9.6307 2.31309C9.56032 2.41128 9.51469 2.52501 9.49771 2.64462C9.48072 2.76422 9.49288 2.88616 9.53314 3.00006H7.32025C6.39825 3.00006 5.54317 3.45817 5.03217 4.22467L3.84857 6.00006H3.74994C3.65056 5.99866 3.55188 6.01702 3.45966 6.05408C3.36743 6.09114 3.28349 6.14616 3.21271 6.21594C3.14194 6.28572 3.08573 6.36888 3.04737 6.46057C3.00901 6.55226 2.98926 6.65067 2.98926 6.75006C2.98926 6.84945 3.00901 6.94786 3.04737 7.03955C3.08573 7.13124 3.14194 7.2144 3.21271 7.28418C3.28349 7.35396 3.36743 7.40899 3.45966 7.44605C3.55188 7.48311 3.65056 7.50147 3.74994 7.50006H20.2499C20.3493 7.50147 20.448 7.48311 20.5402 7.44605C20.6324 7.40899 20.7164 7.35396 20.7872 7.28418C20.8579 7.2144 20.9141 7.13124 20.9525 7.03955C20.9909 6.94786 21.0106 6.84945 21.0106 6.75006C21.0106 6.65067 20.9909 6.55226 20.9525 6.46057C20.9141 6.36888 20.8579 6.28572 20.7872 6.21594C20.7164 6.14616 20.6324 6.09114 20.5402 6.05408C20.448 6.01702 20.3493 5.99866 20.2499 6.00006H20.1513L18.9677 4.22467C18.4567 3.45817 17.6011 3.00006 16.6796 3.00006H14.4667C14.507 2.88616 14.5192 2.76422 14.5022 2.64462C14.4852 2.52501 14.4396 2.41128 14.3692 2.31309C14.2988 2.2149 14.2057 2.13517 14.0979 2.08067C13.9901 2.02617 13.8707 1.99851 13.7499 2.00006H10.2499ZM4.48627 9.00006L5.56244 19.043C5.71244 20.444 6.88781 21.5001 8.29681 21.5001H15.7031C17.1116 21.5001 18.2869 20.444 18.4374 19.043L19.5136 9.00006H4.48627Z"
                                fill="#FF0000"
                              />
                            </svg>
                          </button>
                          <button
                            onClick={() => handleEditAddress(address)}
                            className="text-orange-500 cursor-pointer"
                            title="Edit shipping address"
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="20"
                              height="20"
                              viewBox="0 0 20 20"
                              fill="none"
                            >
                              <path
                                d="M17.3287 7.26797L18.5487 6.04797C19.8157 4.78097 19.8157 2.71897 18.5487 1.45147C17.9352 0.838465 17.1197 0.501465 16.2502 0.501465C15.3807 0.501465 14.5647 0.838965 13.9517 1.45196L12.7322 2.67147L17.3287 7.26797ZM11.6717 3.73197L2.63723 12.7665C2.44473 12.959 2.29823 13.1965 2.21323 13.454L0.538232 18.5145C0.448732 18.7835 0.519232 19.08 0.719732 19.2805C0.863232 19.4235 1.05423 19.5 1.25023 19.5C1.32923 19.5 1.40873 19.4875 1.48623 19.462L6.54523 17.7865C6.80373 17.7015 7.04173 17.555 7.23423 17.362L16.2682 8.32797L11.6717 3.73197Z"
                                fill="#EB6309"
                              />
                            </svg>
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Default Billing Address */}
              <div>
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium text-gray-800">
                    Default billing address
                  </h3>
                  <button
                    type="button"
                    onClick={() => handleAddAddress("bill_to")}
                    className="bg-[#EB6309] text-white px-3 py-1.5 rounded-full hover:bg-[#D45A08] transition-colors text-sm cursor-pointer"
                  >
                    Add New
                  </button>
                </div>

                {/* Billing Address List */}
                <div className="space-y-3">
                  {billingAddresses.map((address) => (
                    <div
                      key={address.id}
                      className="border border-gray-200 rounded-md p-3 relative"
                    >
                      <div className="flex items-start">
                        <div className="mr-3 mt-1">
                          <input
                            type="radio"
                            name="default-billing"
                            checked={address.id === defaultBillingId}
                            onChange={() =>
                              setDefaultAddress(address.id, "bill_to")
                            }
                            className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300"
                            title="Select as default billing address"
                          />
                        </div>
                        <div className="w-full">
                          <p className="font-medium">{address.clinic_name}</p>
                          <p className="text-gray-600">{`${address.street_address}, (#${address.postal_code}), ${address.city}`}</p>
                          <p className="text-gray-600">{address.phone_number}</p>
                        </div>
                        <div className="flex space-x-2 ml-2">
                          <button
                            onClick={() =>
                              handleDeleteAddress("billing", address.id)
                            }
                            className="text-red-500 cursor-pointer"
                            title="Delete billing address"
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="24"
                              height="24"
                              viewBox="0 0 24 24"
                              fill="none"
                            >
                              <path
                                d="M10.2499 2.00006C10.1291 1.99851 10.0098 2.02617 9.90195 2.08067C9.79413 2.13517 9.70108 2.2149 9.6307 2.31309C9.56032 2.41128 9.51469 2.52501 9.49771 2.64462C9.48072 2.76422 9.49288 2.88616 9.53314 3.00006H7.32025C6.39825 3.00006 5.54317 3.45817 5.03217 4.22467L3.84857 6.00006H3.74994C3.65056 5.99866 3.55188 6.01702 3.45966 6.05408C3.36743 6.09114 3.28349 6.14616 3.21271 6.21594C3.14194 6.28572 3.08573 6.36888 3.04737 6.46057C3.00901 6.55226 2.98926 6.65067 2.98926 6.75006C2.98926 6.84945 3.00901 6.94786 3.04737 7.03955C3.08573 7.13124 3.14194 7.2144 3.21271 7.28418C3.28349 7.35396 3.36743 7.40899 3.45966 7.44605C3.55188 7.48311 3.65056 7.50147 3.74994 7.50006H20.2499C20.3493 7.50147 20.448 7.48311 20.5402 7.44605C20.6324 7.40899 20.7164 7.35396 20.7872 7.28418C20.8579 7.2144 20.9141 7.13124 20.9525 7.03955C20.9909 6.94786 21.0106 6.84945 21.0106 6.75006C21.0106 6.65067 20.9909 6.55226 20.9525 6.46057C20.9141 6.36888 20.8579 6.28572 20.7872 6.21594C20.7164 6.14616 20.6324 6.09114 20.5402 6.05408C20.448 6.01702 20.3493 5.99866 20.2499 6.00006H20.1513L18.9677 4.22467C18.4567 3.45817 17.6011 3.00006 16.6796 3.00006H14.4667C14.507 2.88616 14.5192 2.76422 14.5022 2.64462C14.4852 2.52501 14.4396 2.41128 14.3692 2.31309C14.2988 2.2149 14.2057 2.13517 14.0979 2.08067C13.9901 2.02617 13.8707 1.99851 13.7499 2.00006H10.2499ZM4.48627 9.00006L5.56244 19.043C5.71244 20.444 6.88781 21.5001 8.29681 21.5001H15.7031C17.1116 21.5001 18.2869 20.444 18.4374 19.043L19.5136 9.00006H4.48627Z"
                                fill="#FF0000"
                              />
                            </svg>
                          </button>
                          <button
                            onClick={() => handleEditAddress(address)}
                            className="text-orange-500 cursor-pointer"
                            title="Edit billing address"
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="20"
                              height="20"
                              viewBox="0 0 20 20"
                              fill="none"
                            >
                              <path
                                d="M17.3287 7.26797L18.5487 6.04797C19.8157 4.78097 19.8157 2.71897 18.5487 1.45147C17.9352 0.838465 17.1197 0.501465 16.2502 0.501465C15.3807 0.501465 14.5647 0.838965 13.9517 1.45196L12.7322 2.67147L17.3287 7.26797ZM11.6717 3.73197L2.63723 12.7665C2.44473 12.959 2.29823 13.1965 2.21323 13.454L0.538232 18.5145C0.448732 18.7835 0.519232 19.08 0.719732 19.2805C0.863232 19.4235 1.05423 19.5 1.25023 19.5C1.32923 19.5 1.40873 19.4875 1.48623 19.462L6.54523 17.7865C6.80373 17.7015 7.04173 17.555 7.23423 17.362L16.2682 8.32797L11.6717 3.73197Z"
                                fill="#EB6309"
                              />
                            </svg>
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </form>
        )}
      </div>
      <AddressModal
        modalOpen={addressModalOpen}
        modalType={addressModalType}
        addressType={addressType}
        modalData={addressModalData}
        handleAddressChange={handleAddressChange}
        handleAddressSubmit={handleAddressSubmit}
        setModalOpen={setAddressModalOpen}
      />
      <DeleteConfirmationModal
        confirmModalOpen={confirmModalOpen}
        addressToDelete={addressToDelete}
        cancelDeleteAddress={cancelDeleteAddress}
        confirmDeleteAddress={confirmDeleteAddress}
      />
    </>
  );
};

export default DrProfile;
