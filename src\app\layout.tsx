import type { <PERSON>ada<PERSON> } from "next";
import "./globals.css";
import { ToastContainer } from "react-toastify";
import { MantineProvider } from "@mantine/core";
import { SessionProvider } from "@/components/sessionWrapper/SessionProvider";

export const metadata: Metadata = {
  title: "Orthodontic",
  description: "Orthodontic",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (

    <html lang="en">
      <body className={`antialiased`} suppressHydrationWarning>
        
        <SessionProvider>
          <MantineProvider withGlobalClasses>
            <ToastContainer position="top-right" />
            {children}
          </MantineProvider>
        </SessionProvider>
      </body>
    </html>
  );
}