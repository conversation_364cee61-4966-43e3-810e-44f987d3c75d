import Image from "next/image";
import closeIcon from "../../../public/svgs/icons8_close 1.svg";
import addIcon from "../../../public/svgs/icons8_add 1.svg";
import { FieldErrors, UseFormRegister, UseFormSetValue, UseFormWatch } from "react-hook-form";
import { ScanFormData } from "./PatientRecords";
import graphyLogo from "../../../public/images/logo.png"
import { useEffect, useState } from "react";
import { PatientFileData } from "@/types/types";

interface RadiographsSectionProps {
    register: UseFormRegister<ScanFormData>;
    watch: UseFormWatch<ScanFormData>;
    setValue: UseFormSetValue<ScanFormData>
    errors: FieldErrors<ScanFormData>
    patientData: PatientFileData | null
}

const RadiographsSection: React.FC<RadiographsSectionProps> = ({ 
    setValue,
    watch,
    patientData,
    // errors
}) => {
    console.log("🚀 ~ patientData:", patientData)
    const [radioGraph1, setRadioGraph1] = useState<File | string | null>(null);
    const [radioGraph2, setRadioGraph2] = useState<File | string | null>(null);

    // Watch form data for changes from parent (PatientRecords prefill)
    useEffect(() => {
        const radiographs = watch("radiographs");
        if (radiographs) {
            if (radiographs.radioGraph1) {
                setRadioGraph1(radiographs.radioGraph1);
            }
            if (radiographs.radioGraph2) {
                setRadioGraph2(radiographs.radioGraph2);
            }
        }
    }, [watch("radiographs")]);

    // Helper function to get preview URL
    const getPreviewUrl = (file: File | string | null): string => {
        if (!file) return '';
        if (typeof file === 'string') return file; // URL string
        return URL.createObjectURL(file); // File object
    };

    const handleRadiographChange1 = (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = Array.from(e.target.files || []);
        const file = files[0];
        if (file) {
            setRadioGraph1(file);
            setValue("radiographs.radioGraph1", file);
        }
    };

    const handleRadiographChange2 = (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = Array.from(e.target.files || []);
        const file = files[0];
        if (file) {
            setRadioGraph2(file);
            setValue("radiographs.radioGraph2", file);
        }
    };

    const handleRemoveRadiograph1 = () => {
        setRadioGraph1(null);
        setValue("radiographs.radioGraph1", null);
    };

    const handleRemoveRadiograph2 = () => {
        setRadioGraph2(null);
        setValue("radiographs.radioGraph2", null);
    };

    return (
        <div className="mb-8">
            <h2 className="text-2xl font-bold text-dark mb-4">Radiographs</h2>

            <div className="relative grid 2xl:grid-cols-2 grid-cols-1 gap-4 p-8 !bg-primaryLight rounded-[10px]">
                <div className="bg-white rounded-xl">
                    <h2 className="px-5 py-5 font-semibold text-lg text-dark">Panorama</h2>
                    <div className="relative rounded-[10px] p-2 flex flex-col items-center justify-center">
                        <div className="relative w-full h-[200px]">
                            {radioGraph1 ? (
                                <Image
                                    src={getPreviewUrl(radioGraph1)}
                                    alt="Uploaded radiograph 1"
                                    fill
                                    className="w-full object-cover rounded-b-xl"
                                />
                            ) : (
                                <Image src={graphyLogo} alt="X-ray left placeholder" />
                            )}
                        </div>
                        <div className="flex justify-end w-full absolute bottom-[6%] right-[5%]">
                            {radioGraph1 ? (
                                <button
                                    onClick={() => handleRemoveRadiograph1()}
                                    type="button"
                                    className="bg-danger/50 text-white rounded-full p-1 w-5 h-5 flex items-center justify-center"
                                >
                                    <Image src={closeIcon} width={1000} height={1000} alt="Cross icon" className="w-5 h-5 cursor-pointer" />
                                </button>
                            ) : (
                                <>
                                    <label htmlFor="file-upload-1" className="cursor-pointer">
                                        <Image
                                            width={1000}
                                            height={1000}
                                            src={addIcon} alt="Upload icon" className="w-8 h-8" />
                                    </label>
                                    <input
                                        id="file-upload-1"
                                        type="file"
                                        accept="image/*"
                                        onChange={(e) => handleRadiographChange1(e)}
                                        className="hidden"
                                    />
                                </>
                            )}
                        </div>
                    </div>
                </div>
                <div className="bg-white rounded-xl">
                    <h2 className="px-5 py-5 font-semibold text-lg text-dark">Lateral Cephalogram</h2>
                    <div className="relative rounded-[10px] p-2 flex flex-col items-center justify-center">
                        <div className="relative w-full h-[200px]">
                            {radioGraph2 ? (
                                <Image
                                    src={getPreviewUrl(radioGraph2)}
                                    alt="Uploaded radiograph 2"
                                    fill
                                    className="w-full object-cover rounded-b-xl rounded-[inherit]"
                                />
                            ) : (
                                <Image src={graphyLogo} alt="X-ray right placeholder" />
                            )}
                        </div>

                        <div className="flex justify-end w-full absolute bottom-[6%] right-[5%]">
                            {radioGraph2 ? (
                                <button
                                    onClick={() => handleRemoveRadiograph2()}
                                    type="button"
                                    className="bg-danger/50 text-white rounded-full p-1 w-5 h-5 flex items-center justify-center"
                                >
                                    <Image src={closeIcon} alt="Cross icon" className="w-5 h-5 cursor-pointer" />
                                </button>
                            ) : (
                                <>
                                    <label htmlFor="file-upload-2" className="cursor-pointer">
                                        <Image src={addIcon} alt="Upload icon" className="w-8 h-8" />
                                    </label>
                                    <input
                                        id="file-upload-2"
                                        type="file"
                                        accept="image/*"
                                        onChange={(e) => handleRadiographChange2(e)}
                                        className="hidden"
                                    />
                                </>
                            )}
                        </div>
                    </div>
                </div>
            </div>
            {/* {errors.radiographs?.radioGraph1?.message && <p className="text-danger my-1">{typeof errors.radiographs?.radioGraph1?.message == "string" && errors.radiographs?.radioGraph1?.message}</p>} */}
        </div>
    );
};

export default RadiographsSection;