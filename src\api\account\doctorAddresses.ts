import { API_ROUTES } from "@/utils/ApiRoutes";
import { fetchApi } from "../getapis";

// src/app/api/doctorAddresses.ts

export interface DoctorAddress {
  id: string;
  doctor_id: string;
  clinic_name: string;
  street_address: string;
  city: string;
  postal_code: string;
  phone_number: string;
  created_at: string;
  updated_at: string;
  address_type: 'ship_to' | 'bill_to' | string;
}

export async function fetchDoctorAddresses(): Promise<DoctorAddress[] | null> {
  return await fetchApi<DoctorAddress[]>(API_ROUTES.ADRESSES.GET_ADRESS);
}