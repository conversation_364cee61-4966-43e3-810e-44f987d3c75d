"use client"
import React, { useEffect, useRef, useState } from 'react';
import FormHeading from '../reuseable/FormHeading';
import CustomInput from '../reuseable/CustomInput';
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import RoundRadioButton from '../reuseable/RoundRadioButton';
import '@/app/globals.css';
import { useRouter } from 'next/navigation';
import FormWrapper from '../reuseable/FormWrapper';
import CustomDateInput from '../reuseable/CustomDateInput';
import { Address, PatientFileData } from '@/types/types';
// import { createPatient } from '@/utils/ApisHelperFunction';
import { Encrytion, storeToken } from '@/app/lib/auth';
import { toast } from 'react-toastify';

const patientDataSchema = z.object({
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  gender: z.enum(["male", "female"], {
    errorMap: () => ({ message: "Gender is required" }),
  }),
  birthdate: z.coerce.date({
    errorMap: () => ({ message: "Birthdate is required and must be a valid date" }),
  }),
  country: z.enum(["saudi-arabia", "bahrain"], {
    errorMap: () => ({ message: "Country is required" }),
  }),
  shipAddress: z.string().min(1, "Shipping to address is required"), // Allow any non-empty string
  billAddress: z.string().min(1, "Billing address is required"), // Allow any non-empty string
});

type UserFormData = z.infer<typeof patientDataSchema>;
const countryOptions = [
  { value: "saudi-arabia", label: "Saudi Arabia" },
  { value: "bahrain", label: "Bahrain" },
];


const MainPatientData: React.FC<{ data: Address[]; patientData: PatientFileData | null }> = ({ data, patientData }) => {
  const router = useRouter()
  const { register, handleSubmit, watch, setValue, formState: { errors }, } = useForm<UserFormData>({ resolver: zodResolver(patientDataSchema), });
  const [gender, setGender] = useState<string>("")
  const [shipAddressid, setShipAddressid] = useState<string>("")
  const [billAddress, setBillAddress] = useState<string>("")
  const [countryOpen, setCountryOpen] = useState(false);
  const [countryLabel, setCountryLabel] = useState<string>("");
  const countryDropdownRef = useRef<HTMLDivElement>(null);

  // Prefill form if patientData is available
  useEffect(() => {
    if (patientData) {
      setValue("firstName", patientData.first_name || "");
      setValue("lastName", patientData.last_name || "");
      setValue("gender", (patientData.gender === "male" || patientData.gender === "female" ? patientData.gender : "") as "male" | "female");
      if (patientData.dob) {
        setValue("birthdate", new Date(patientData.dob));
      }
      setValue("country", (patientData.country|| countryLabel || "") as "saudi-arabia" | "bahrain");
      setShipAddressid(patientData.ship_to_office || "");
      setBillAddress(patientData.bill_to_office || "");
      setGender(patientData.gender || "");
      
      setValue("shipAddress", patientData.ship_to_office || "");
      setValue("billAddress", patientData.bill_to_office || "");
    }
  }, [patientData, setValue]);

  // Click outside handler
  useEffect(() => {
    if (!countryOpen) return;
    const handleClickOutside = (event: MouseEvent) => {
      if (
        countryDropdownRef.current &&
        !countryDropdownRef.current.contains(event.target as Node)
      ) {
        setCountryOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [countryOpen]);



  const onSubmit = async (formData: UserFormData): Promise<void> => {

    const patientData = {
      country: formData.country,
      step: "step1",
      first_name: formData.firstName,
      last_name: formData.lastName,
      dob: formData.birthdate.toISOString().split("T")[0],
      gender: formData.gender,
      ship_to_office: shipAddressid,
      bill_to_office: billAddress,
    };
    const patient = Encrytion(JSON.stringify(patientData));
    storeToken('patientData', patient, true);
    toast.success("Patient data saved successfully");
    router.push("/treatment-plans");
    // try {
    //   const token = getAndDecryptCookie('AccessToken');
    //   if (!token) {
    //     throw new Error("Login is required");
    //   }

    //   // Call the createPatient API
    //   const response = await createPatient(token, patientData);
    //   if (response) {
    //     console.log("Patient created successfully:", response.data);
    //     localStorage.setItem("patientData", JSON.stringify(patientData));

    //   } else {
    //     console.error("Failed to create patient");
    //   }
    // } catch (error) {
    //   console.error("Error during patient creation:", error);
    // }
  };


  return <>
    <FormWrapper onSubmit={handleSubmit(onSubmit)} onBack={() => router.back()} showCancelButton={true}>
      <div className='col-span-1 flex flex-col justify-between gap-6'>

        <div>
          <FormHeading text='Patient Name*' />

          <div className='flex flex-col gap-2'>
            <CustomInput type='text' placeholder='First Name' className='!py-3.5' register={register("firstName")} error={errors.firstName?.message} />
            <CustomInput type='text' placeholder='Last Name' className='!py-3.5' register={register("lastName")} error={errors.lastName?.message} />
          </div>
        </div>


        <div>
          <FormHeading text='Country*' />
          <div className="relative" ref={countryDropdownRef}>
            <button
              type="button"
              className={`w-full px-4 py-3 border border-gray-300 rounded-full text-left bg-white ${errors.country ? "border-red-500" : ""}`}
              onClick={() => setCountryOpen((open) => !open)}
              tabIndex={0}
            >
              {countryOptions.find(opt => opt.value === watch("country"))?.label || "Select Country"}
            </button>
            {countryOpen && (
              <ul className="absolute z-10 w-full bg-white border border-gray-300 rounded-lg mt-1 shadow">
                {countryOptions.map(option => (
                  <li
                    key={option.value}
                    className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                    onClick={() => {
                      setCountryOpen(false);
                      setCountryLabel(option.label);
                      // setValue is from react-hook-form
                      setValue("country", option.value as "saudi-arabia" | "bahrain", { shouldValidate: true });
                    }}
                  >
                    {option.label}
                  </li>
                ))}
              </ul>
            )}
            {errors.country && (
              <p className="text-red-500 text-sm mt-1">{errors.country.message}</p>
            )}
          </div>
        </div>

        <div>
          <FormHeading text='Patient Gender*' />
          <div className='flex items-center gap-4'>
            <RoundRadioButton
              id='gender-male'
              label='Male'
              value='male'
              name='gender'
              onClick={(e: React.MouseEvent<HTMLInputElement>) => setGender((e.target as HTMLInputElement).value as "male")}
              register={register}
              defaultChecked={gender === 'male'}
            />
            <RoundRadioButton
              id='gender-female'
              label='Female'
              value='female'
              name='gender'
              register={register}
              onClick={(e: React.MouseEvent<HTMLInputElement>) => setGender((e.target as HTMLInputElement).value as "female")}
              defaultChecked={gender === 'female'}
            />
          </div>
          {errors.gender && <p className="text-red-500 text-sm mt-1">{errors.gender.message}</p>}
        </div>


        <div>
          <FormHeading text="Date of Birth*" />
          <div className="">
            <CustomDateInput
              value={watch("birthdate")}
              register={register("birthdate")}
              id="birthDate"
              name="birthdate"
              className=''
              minDate={new Date(1900, 1, 1)} // January 1, 1900
              maxDate={new Date()} // Today
            />
          </div>
          {
            <p className="text-red-500 text-sm mt-1">
              {errors?.birthdate?.message}
            </p>
          }
        </div>

      </div>
      <div className='col-span-1'>
        <div className="flex flex-col gap-4">
          {/* Ship To Office */}
          <div>
            <FormHeading text="Ship To Office*" />
            <div className="flex flex-col gap-5">
              {data
                ?.filter((address) => address.address_type === "ship_to") // Filter addresses with "ship to"
                .map((address) => (
                  <RoundRadioButton
                    key={address?.id}
                    id={`ship-${address?.id}`}
                    label={`${address?.clinic_name} (#${address?.postal_code}), ${address?.street_address}, ${address?.city}`}
                    value={String(address?.id)}
                    name="shipAddress"
                    // defaultChecked={shipAddressid === String(address?.id)}
                    onClick={() => setShipAddressid(String(address?.id))}
                    register={register}
                  />
                ))}
            </div>
            {errors.shipAddress && <p className="text-red-500 text-sm mt-1">{errors?.shipAddress.message}</p>}
          </div>

          <div>
            <FormHeading text='Bill To Office*' />
            <div className="flex flex-col gap-5">
              {data
                ?.filter((address) => address?.address_type === "bill_to")
                .map((address) => (
                  <RoundRadioButton
                    key={address?.id}
                    id={`bill-${address?.id}`}
                    label={`${address?.clinic_name} (#${address?.postal_code}), ${address?.street_address}, ${address?.city}`}
                    value={String(address?.id)}
                    name="billAddress"
                    // defaultChecked={billAddress === String(address?.id)}
                    onClick={() => setBillAddress(String(address?.id))}
                    register={register}
                  />
                ))}
            </div>
            {errors.billAddress && <p className="text-red-500 text-sm mt-1">{errors?.billAddress.message}</p>}
          </div>

        </div>
      </div>
    </FormWrapper>

  </>
};

export default MainPatientData;
