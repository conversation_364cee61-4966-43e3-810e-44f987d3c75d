{"name": "orthodontic-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@headlessui/react": "^2.2.4", "@hookform/resolvers": "^5.0.1", "@mantine/core": "^7.17.3", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "dotenv": "^16.4.7", "framer-motion": "^12.12.2", "js-cookie": "^3.0.5", "mantine-datatable": "^7.17.1", "next": "^15.4.1", "react": "^19.0.0", "react-datepicker": "^8.3.0", "react-dom": "^19.0.0", "react-flatpickr": "^4.0.10", "react-hook-form": "^7.55.0", "react-icons": "^5.5.0", "react-toastify": "^11.0.5", "socket.io-client": "^4.8.1", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/crypto-js": "^4.2.2", "@types/jest": "^29.5.14", "@types/js-cookie": "^3.0.6", "@types/node": "^20", "@types/react": "^19.1.1", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "concurrently": "^8.2.2", "eslint": "^9", "eslint-config-next": "15.2.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.5.3", "tailwindcss": "^4.1.3", "ts-jest": "^29.3.1", "typescript": "^5"}}