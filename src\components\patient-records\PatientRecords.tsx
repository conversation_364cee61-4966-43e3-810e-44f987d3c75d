"use client";

import React, { useEffect } from "react";
import { useForm, FieldErrors } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import { toast } from "react-toastify";

import FormWrapper from "../reuseable/FormWrapper";
import ScanSection from "./ScanOptions";
import PhotosSection from "./PhotosSection";
import RadiographsSection from "./RadioGraphSection";
import GeneralRecords from "./GeneralRecords";
import { submitPatientData } from "@/utils/ApisHelperFunction";
import getAndDecryptCookie from "@/app/lib/auth";
import { PatientFileData } from "@/types/types";

// Extended types for handling both File objects and URLs
export interface DentalPhoto {
    name: string;
    file: File | string | null; // Allow both File and URL string
}

export interface ScanData {
    stlFile1: File | string | null;
    stlFile2: File | string | null;
    cbctFile: File | string | null;
}

export interface RadiographData {
    radioGraph1: File | string | null;
    radioGraph2: File | string | null;
}

export interface GeneralRecordsData {
    files: (File | string)[];
}

// Updated schemas to handle both File and string (URL)
const fileOrUrlSchema = z.union([
    z.instanceof(File),
    z.string().url(),
    z.null()
]);

const photoSchema = z.object({
    name: z.string(),
    file: fileOrUrlSchema,
});

// Zod Schema
const baseSchema = {
    scan: z.object({
        stlFile1: fileOrUrlSchema,
        stlFile2: fileOrUrlSchema,
        cbctFile: fileOrUrlSchema,
    }).superRefine((val, ctx) => {
        if (!val.stlFile1 || !val.stlFile2) {
            ctx.addIssue({
                path: ["stlFile1"],
                code: z.ZodIssueCode.custom,
                message: "Both STL scans are required",
            });
        }
    }),
    photos: z.array(photoSchema).superRefine((val, ctx) => {
        const requiredPhotos = val.filter((photo) => photo.name !== "socialSmile");
        if (requiredPhotos.some((photo) => photo.file == null)) {
            ctx.addIssue({
                path: [],
                code: z.ZodIssueCode.custom,
                message: "All photos are required",
            });
        }
    }),
    generalRecords: z.object({
        files: z.array(z.union([z.instanceof(File), z.string()]))
            .max(10, { message: "You can upload a maximum of 10 files" })
            .optional()
    }),
};

// Final schema with optional radiographs
const PatientRecordsSchema = z.object({
    ...baseSchema,
    radiographs: z.object({
        radioGraph1: fileOrUrlSchema,
        radioGraph2: fileOrUrlSchema,
    }).optional(),
});

export type ScanFormData = z.infer<typeof PatientRecordsSchema>;

const PatientRecords = ({ patientData }: { patientData: PatientFileData | null }) => {
    const router = useRouter();

    const {
        register,
        watch,
        setValue,
        handleSubmit,
        formState: { errors },
    } = useForm<ScanFormData>({
        resolver: zodResolver(PatientRecordsSchema),
        defaultValues: {
            scan: {
                stlFile1: null,
                stlFile2: null,
                cbctFile: null,
            },
            photos: [],
            radiographs: {
                radioGraph1: null,
                radioGraph2: null,
            },
            generalRecords: {
                files: []
            }
        },
    });

    const data = watch();

    useEffect(() => {
        console.log("DATA====>", data);
    }, [data]);

    // Helper function to convert URL to File object
    const urlToFile = async (url: string, filename: string): Promise<File | null> => {
        try {
            const response = await fetch(url);
            const blob = await response.blob();
            return new File([blob], filename, { type: blob.type });
        } catch (error) {
            console.error("Error converting URL to File:", error);
            return null;
        }
    };

    // Prefill form with patientData
    useEffect(() => {
        const loadExistingFiles = async () => {
            if (patientData) {
                // Set scan files
                if (patientData.stlFile1) {
                    setValue("scan.stlFile1", patientData.stlFile1);
                }
                if (patientData.stlFile2) {
                    setValue("scan.stlFile2", patientData.stlFile2);
                }
                if (patientData.cbctFile) {
                    setValue("scan.cbctFile", patientData.cbctFile);
                }

                // Set photo files
                const photoData: DentalPhoto[] = [
                    { name: "profileRepose", file: patientData.profileRepose || null },
                    { name: "buccalRight", file: patientData.buccalRight || null },
                    { name: "buccalLeft", file: patientData.buccalLeft || null },
                    { name: "frontalRepose", file: patientData.frontalRepose || null },
                    { name: "frontalSmiling", file: patientData.frontalSmiling || null },
                    { name: "labialAnterior", file: patientData.labialAnterior || null },
                    { name: "occlussalLower", file: patientData.occlussalLower || null },
                    { name: "occlussalUpper", file: patientData.occlussalUpper || null },
                ].filter(photo => photo.file !== null);

                setValue("photos", photoData);

                if (patientData.radioGraph1 || patientData.radioGraph2) {
                    setValue("radiographs", {
                        radioGraph1: patientData.radioGraph1 || null,
                        radioGraph2: patientData.radioGraph2 || null,
                    });
                }
            }
        };

        loadExistingFiles();
    }, [patientData, setValue]);

    // Helper function to check if value is File or URL
    const isFile = (value: File | string | null): value is File => {
        return value instanceof File;
    };

    const onSubmit = async (data: ScanFormData) => {
        console.log("🚀 ~ onSubmit ~ data:", data);
        try {
            const token = getAndDecryptCookie("AccessToken");
            const patientId = getAndDecryptCookie("patientId");

            if (!token || !patientId) {
                console.error("Token or Patient ID is missing");
                toast.error("Authentication required.");
                return;
            }

            // Convert URLs to Files if needed (or handle them appropriately)
            const getFileFromValue = async (value: File | string | null, fieldName: string): Promise<File | null> => {
                if (!value) return null;
                if (isFile(value)) return value;
                // If it's a URL, convert to File
                return await urlToFile(value, fieldName);
            };

            // Process scan files
            const stlFile1 = await getFileFromValue(data.scan.stlFile1, "stlFile1.zip");
            const stlFile2 = await getFileFromValue(data.scan.stlFile2, "stlFile2.zip");
            const cbctFile = await getFileFromValue(data.scan.cbctFile, "cbctFile.zip");

            // Process photo files
            const photoFiles: { [key: string]: File | null } = {};
            for (const photo of data.photos) {
                photoFiles[photo.name] = await getFileFromValue(photo.file, `${photo.name}.jpg`);
            }

            // Process radiograph files
            const radioGraph1 = data.radiographs?.radioGraph1 ? 
                await getFileFromValue(data.radiographs.radioGraph1, "radioGraph1.jpg") : null;
            const radioGraph2 = data.radiographs?.radioGraph2 ? 
                await getFileFromValue(data.radiographs.radioGraph2, "radioGraph2.jpg") : null;

            // Validate required files
            if (!stlFile1 || !stlFile2 || !cbctFile ||
                !photoFiles.profileRepose || !photoFiles.buccalRight || 
                !photoFiles.buccalLeft || !photoFiles.frontalRepose ||
                !photoFiles.frontalSmiling || !photoFiles.labialAnterior || 
                !photoFiles.occlussalLower || !photoFiles.occlussalUpper) {
                toast.error("All required files must be uploaded.");
                return;
            }

            // Filter generalRecords to only include File objects
            const generalRecordsFiles = (data.generalRecords?.files || []).filter(
                (file): file is File => file instanceof File
            );

            const records = {
                stlFile1,
                stlFile2,
                cbctFile,
                profileRepose: photoFiles.profileRepose,
                buccalRight: photoFiles.buccalRight,
                buccalLeft: photoFiles.buccalLeft,
                frontalRepose: photoFiles.frontalRepose,
                frontalSmiling: photoFiles.frontalSmiling,
                labialAnterior: photoFiles.labialAnterior,
                occlussalLower: photoFiles.occlussalLower,
                occlussalUpper: photoFiles.occlussalUpper,
                radioGraph1,
                radioGraph2,
                generalRecords: generalRecordsFiles
            };

            const response = await submitPatientData(token, patientId, records);

            if (response) {
                console.log("Patient data submitted successfully:", response);
                toast.success("Patient data submitted successfully.");
                router.push("/case-prescription");
            } else {
                console.error("Failed to submit patient data");
                toast.error("Failed to submit patient data.");
            }
        } catch (error) {
            console.error("Error during patient data submission:", error);
            toast.error("An error occurred while submitting patient data.");
        }
    };

    const onError = (errors: FieldErrors<ScanFormData>) => {
        toast.error("Please fill all the required fields.");
        console.log("❌ Form validation errors:", errors);
    };

    return (
        <FormWrapper
            classNames="!grid-cols-1"
            onSubmit={handleSubmit(onSubmit, onError)}
            onBack={() => { }}
        >
            <div className="flex flex-col gap-6">
                <PhotosSection
                    register={register}
                    watch={watch}
                    setValue={setValue}
                    errors={errors}
                    patientData={patientData}
                />
                <RadiographsSection
                    register={register}
                    watch={watch}
                    setValue={setValue}
                    errors={errors}
                    patientData={patientData}
                />
                <ScanSection
                    register={register}
                    watch={watch}
                    setValue={setValue}
                    errors={errors}
                    patientData={patientData}
                />
                <GeneralRecords
                    register={register}
                    watch={watch}
                    setValue={setValue}
                    errors={errors}
                    patientData={patientData}
                />
            </div>
        </FormWrapper>
    );
};

export default PatientRecords;
