// const API_BASE_URL = 'https://orthodontic.desinoir.com/api/v1';
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;
export const API_ROUTES = {
  AUTH: {
    LOGIN: `${API_BASE_URL}/auth/login-user`,
    FORGET_PASSWORD: `${API_BASE_URL}/auth/forget-password`,
    CHECK_SESSION: `${API_BASE_URL}/auth/check-session`,
  },
  PATIENT: {
    GET_ALL_PATIENTS: `${API_BASE_URL}/doctor/patients`,
    GET_ALL_PATIENTS_FOR_SPECIALIST: `${API_BASE_URL}/specialist/patients`,
    GET_PATIENT_BY_ID: `${API_BASE_URL}/doctor/patients`,
    GET_PATIENT_BY_ID_FOR_SPECIALIST:`${API_BASE_URL}/specialist/patient`,
    ADD_PATIENT: `${API_BASE_URL}/doctor/patients`,
    ADD_CLINICAL_DATA: `${API_BASE_URL}/doctor/patients`,
    ADD_RECORDS_DATA: `${API_BASE_URL}/doctor/patients`,
    ADD_CASE_PERSCRIPTION: `${API_BASE_URL}/doctor/patients`,
VERSION_REVIEW: `${API_BASE_URL}/doctor/patients`,
    ADD_REFINEMENT_ALIGNER: `${API_BASE_URL}/doctor/patients`,
    ADD_REPLACEMENT_ALIGNER: `${API_BASE_URL}/doctor/patients/aligner-replacements`,
    ADD_4DGRAPHY_RETAINER: `${API_BASE_URL}/doctor/patients/4dgraphic_retainer`,
    ADD_CBCT_FILE: `${API_BASE_URL}/doctor/patients`,
    GET_SPECIALIST: `${API_BASE_URL}/doctor/patients`,
  },
  ADRESSES: {
    GET_ADRESS: `${API_BASE_URL}/doctor/addresses`,
    ADD_ADDRESS: `${API_BASE_URL}/doctor/addresses`,
    DELETE_ADDRESS: `${API_BASE_URL}/doctor/addresses`,
    UPDATE_ADDRESS: `${API_BASE_URL}/doctor/addresses`,
  },
  PLAN: {
    GET_PLANS: `${API_BASE_URL}/doctor/plans`,
  },
  EMPLOYEE: {
    GET_EMPLOYEES: `${API_BASE_URL}/doctor/employees`,
    CREATE_EMPLOYEE: `${API_BASE_URL}/doctor/employees`,
    GET_EMPLOYEE_BY_ID: `${API_BASE_URL}/doctor/employee`,
  },
  RETAINER: {
    CREATE_RETAINER_PATIENT: `${API_BASE_URL}/doctor/retainer/patients`,
  },
  PROFILE: {
    GET_PROFILE: `${API_BASE_URL}/auth/profile`,
    UPDATE_PROFILE: `${API_BASE_URL}/auth/profile`,
  }
};