'use client'
import { Decrytion, getToken } from '@/app/lib/auth';
// import { getToken, Decrytion } from '@/api/getapis';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { useState, useEffect } from 'react'

const PreviewPage = () => {
    const [modalOpen, setModalOpen] = useState(false);
    const [actionType, setActionType] = useState<"approve" | "reject" | null>(null);
    const [reason, setReason] = useState("");
    const searchParams = useSearchParams();
    const [iframeUrl, setIframeUrl] = useState("");
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        const url = searchParams.get('url');
        if (url) {
            try {
                const decodedUrl = decodeURIComponent(url);
                // Get the encrypted token
                const encryptedToken = getToken("AccessToken");
                // Decrypt the token
                const token = encryptedToken ? Decrytion(encryptedToken) : null;
                
                if (token) {
                    // Add token as Authorization header
                    const urlObj = new URL(decodedUrl);
                    urlObj.searchParams.append('Authorization', `Bearer ${token}`);
                    setIframeUrl(urlObj.toString());
                } else {
                    console.error("No auth token found");
                    // Redirect to login if no token
                    navigate.push('/login');
                }
            } catch (error) {
                console.error("Error processing URL:", error);
            } finally {
                setIsLoading(false);
            }
        }
    }, [searchParams]);

    const navigate = useRouter();

    const handleOpenModal = (type: "approve" | "reject") => {
        setActionType(type);
        setReason("");
        setModalOpen(true);
    };

    const handleCloseModal = () => {
        setModalOpen(false);
        setActionType(null);
        setReason("");
    };

    const handleSubmit = async () => {
        if (!reason.trim()) {
            alert("Please enter a reason");
            return;
        }

        try {
            const encryptedToken = getToken("AccessToken");
            const token = encryptedToken ? Decrytion(encryptedToken) : null;

            if (!token) {
                throw new Error("No authentication token found");
            }

            // Here you would typically make an API call to submit the review
            console.log("Submitting review:", {
                action: actionType,
                reason: reason
            });

            handleCloseModal();
            // Optionally redirect or show success message
        } catch (error) {
            console.error("Error submitting review:", error);
            alert("Failed to submit review");
        }
    };

    return (
        <div className="relative min-h-screen">
            <div className='w-full h-screen'>
                {isLoading ? (
                    <div className="flex items-center justify-center h-full">
                        <p>Loading preview...</p>
                    </div>
                ) : iframeUrl ? (
                    <iframe
                        src={iframeUrl}
                        width="100%"
                        height="100%"
                        style={{ border: "none" }}
                        title="NemoCloud Viewer"
                        sandbox="allow-same-origin allow-scripts allow-popups allow-forms allow-credentials"
                        allow="fullscreen"
                        referrerPolicy="origin"
                    />
                ) : (
                    <div className="flex items-center justify-center h-full">
                        <p>Failed to load preview</p>
                    </div>
                )}
            </div>

            {/* Action Buttons */}
            <div className="fixed top-4 right-4 flex gap-4">
                <button
                    className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
                    onClick={() => handleOpenModal("approve")}
                >
                    Accept
                </button>
                <button
                    className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
                    onClick={() => handleOpenModal("reject")}
                >
                    Reject
                </button>
            </div>

            {/* Modal */}
            {modalOpen && (
                <div className="fixed inset-0 flex items-center justify-center bg-black/70 z-50">
                    <div className="bg-white rounded-lg shadow-lg p-6 w-[500px]" onClick={e => e.stopPropagation()}>
                        <h2 className="text-lg font-semibold mb-4">
                            {actionType === "approve" ? "Approval" : "Rejection"} Reason
                        </h2>
                        <textarea
                            className="w-full border rounded p-2 mb-4 min-h-[100px]"
                            placeholder={`Enter reason for ${actionType === "approve" ? "approval" : "rejection"}...`}
                            value={reason}
                            onChange={e => setReason(e.target.value)}
                        />
                        <div className="flex justify-end gap-2">
                            <button
                                className="px-4 py-2 bg-gray-300 rounded hover:bg-gray-400"
                                onClick={handleCloseModal}
                            >
                                Cancel
                            </button>
                            <button
                                className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                                onClick={handleSubmit}
                            >
                                Submit
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default PreviewPage;