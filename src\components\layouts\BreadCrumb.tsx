"use client"
import React, { useEffect, useState } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";

interface props {
    trailNumbers: Record<string, number>
}

const formatTrailLink = (trailLink: string): string => {
    if (trailLink === "patient-retainer") {
        return "Patient Data";
    }
    if (trailLink === "treatment-retainer-option") {
        return "Treatment Plans";
    }
    if (trailLink === "patient-retainer-record") {
        return "Patient Records";
    }
    if (trailLink === "retainer-info") {
        return "Retainer Info";
    }
    return trailLink
        .split("-")
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(" ");
};


const BreadCrumbs: React.FC<props> = ({ trailNumbers }) => {
    const [option, setOption] = useState<string | null>()
    useEffect(() => {
        const op = localStorage.getItem("treatmentOption")
        setOption(op)
    }, [])
    const pathname: string = usePathname();
    return (
        <div className="bg-white rounded-full py-2.5 xl:px-6 px-4 flex gap-2 font-medium">
            {Object.keys(trailNumbers).map((trailLink: string, index: number) => {
                const disabled = (option == "4d-graphy-retainer" && trailLink == "clinical-conditions")

                const color =
                    trailNumbers[pathname.replace("/", "")] >= index + 1
                        ? "text-dark"
                        : "text-gray";
                return (
                    <React.Fragment key={trailLink}>
                        <Link className={`${color} max-xl:text-[14px] xl:text-[16px]  ${disabled && "pointer-events-none"}`} href={`/${trailLink}`}>
                            {formatTrailLink(trailLink)}
                        </Link>
                        {index < Object.keys(trailNumbers).length - 1 ? (
                            <span className={`${color}`}>/</span>
                        ) : null}
                    </React.Fragment>
                );
            })}
        </div>
    );
};


export default BreadCrumbs;