export interface Refinement {
  id: number;
  description: string;
  created_at: string;
  updated_at: string;
}

export interface AlignerReplacement {
  id: number;
  aligner_id: number;
  reason: string;
  created_at: string;
  updated_at: string;
}

export interface Retainer {
  id: number;
  patient_id: number;
  other_details?: string;
  stl_file1?: string | null;
  stl_file2?: string | null;
  cbct_file?: string | null;
  profile_repose?: string | null;
  buccal_right?: string | null;
  buccal_left?: string | null;
  frontal_repose?: string | null;
  frontal_smiling?: string | null;
  labial_anterior?: string | null;
  occlusal_lower?: string | null;
  occlusal_upper?: string | null;
  radiograph1?: string | null;
  radiograph2?: string | null;
  created_at?: string;
  updated_at?: string;
}

export interface RefinementAligner {
  id: number;
  patient_id: number;
  refinement_details?: string;
  upper_impression?: string | null;
  lower_impression?: string | null;
  profileRepose?: string | null;
  buccalRight?: string | null;
  buccalLeft?: string | null;
  frontalRepose?: string | null;
  frontalSmiling?: string | null;
  labialAnterior?: string | null;
  occlussalLower?: string | null;
  occlussalUpper?: string | null;
  radioGraph1?: string | null;
  radioGraph2?: string | null;
  created_at?: string;
  updated_at?: string;
}

export interface PatientFile {
  id: number;
  patient_id: number;
  file_name: string;
  reason?: string;
  created_at?: string;
  updated_at?: string;
}

export interface PatientFileData {
  id: number;
  doctor_id: number;
  first_name: string;
  last_name: string;
  email: string;
  dob: string;
  gender: string;
  ship_to_office: string;
  bill_to_office: string;
  plan_id: number;
  clinical_conditions: string | null;
  general_notes: string | null;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  stlFile1: string | null;
  stlFile2: string | null;
  cbctFile: string | null;
  profileRepose: string | null;
  buccalRight: string | null;
  buccalLeft: string | null;
  frontalRepose: string | null;
  frontalSmiling: string | null;
  labialAnterior: string | null;
  occlussalLower: string | null;
  occlussalUpper: string | null;
  radioGraph1: string | null;
  radioGraph2: string | null;
  data: {
    chief_complaint: string;
    treatment_goals: string;
    notes: string;
  };
  country: string;
  plan: {
    id: number;
    name: string;
    created_at: string;
    updated_at: string;
    type: string;
    duration_years: number | null;
    expiration_date: string | null;
  };
  shipToOffice: {
    id: number;
    doctor_id: number;
    clinic_name: string;
    street_address: string;
    city: string;
    postal_code: string;
    phone_number: string;
    created_at: string;
    updated_at: string;
  };
  billToOffice: {
    id: number;
    doctor_id: number;
    clinic_name: string;
    street_address: string;
    city: string;
    postal_code: string;
    phone_number: string;
    created_at: string;
    updated_at: string;
  };
  refinements: Refinement[];
  alignerReplacements: AlignerReplacement[];
  retainers: Retainer[];
  refinementsAligner: RefinementAligner[];
  patientFiles: PatientFile[];
  status: string;
  versions: PatientFileVersion[];
  uuid: string | null;
}

export interface PatientData {
  id: number;
  doctor_id: number;
  first_name: string;
  last_name: string;
  email: string;
  dob: string;
  gender: string;
  ship_to_office: string;
  bill_to_office: string;
  plan_id: number;
  clinical_conditions: string | null;
  general_notes: string | null;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  stlFile1: string | null;
  stlFile2: string | null;
  cbctFile: string | null;
  profileRepose: string | null;
  buccalRight: string | null;
  buccalLeft: string | null;
  frontalRepose: string | null;
  frontalSmiling: string | null;
  labialAnterior: string | null;
  occlussalLower: string | null;
  occlussalUpper: string | null;
  radioGraph1: string | null;
  radioGraph2: string | null;
  data: string | null;
  country: string;
  plan_name: string | null;
  status: string | null;
  uuid: string | null;
}

export interface Address {
  id: number;
  clinic_name: string;
  street_address: string;
  city: string;
  postal_code: string;
  phone_number: string;
  address_type: string;
}

export interface Plan {
  id: number;
  name: string;
  type: string;
  duration_years: number | null;
  expiration_date: string | null;
  created_at: string;
  updated_at: string;
}
export interface PlansResponse {
  plans: Plan[]; // Array of plans
  message: string; // Success message
}

export interface CreatePatientResponse {
  status: number;
  success: boolean;
  data: {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
    dob: string;
    gender: string;
    country: string;
    ship_to_office: string;
    bill_to_office: string;
    created_at: string;
    updated_at: string;
  };
  message: string;
}

export interface EmployeeApiResponse {
  status: number;
  success: boolean;
  data: {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
    username: string;
    profile_image: string | null;
    role: string;
    salutation: string;
    practice_phone_number: string;
    mobile: string;
    profession: string;
  };
  message: string;
}

export interface DoctorProfileApiResponse {
  status: number;
  success: boolean;
  data: {
    id: number;
    email: string;
    username: string;
    first_name: string;
    last_name: string;
    role_id: number;
    is_verified: boolean;
    profile_image: string | null;
    // Add more fields as needed based on actual API response
  };
  message: string;
}

export interface UpdateDoctorProfilePayload {
  first_name: string;
  last_name: string;
  email: string;
  username: string;
}

export interface AddAddressPayload {
  clinic_name: string;
  street_address: string;
  city: string;
  postal_code: string;
  phone_number: string;
  address_type: "bill_to" | "ship_to";
  token: string; // JWT token from localStorage or wherever it's stored
}

export interface DeleteAddressPayload {
  addressId: string | number;
  token: string; // JWT token from localStorage or wherever it's stored
}

export interface UpdateAddressPayload {
  addressId: string | number;
  clinic_name: string;
  street_address: string;
  city: string;
  postal_code: string;
  phone_number: string;
  address_type: "bill_to" | "ship_to";
  token: string; // JWT token from localStorage or wherever it's stored
}

export interface LoginResponse extends ApiResponse {
  data?: {
    accessToken?: string;
    user?: Record<string, unknown>;
  };
}

export interface ForgetPasswordResponse extends ApiResponse {
  data?: {
    message?: string;
  };
}

export interface ApiResponse {
  status: number;
  success: boolean;
  message: string;
  data?: Record<string, unknown>;
}

export interface AddAddressPayload {
  clinic_name: string;
  street_address: string;
  city: string;
  postal_code: string;
  phone_number: string;
  address_type: "bill_to" | "ship_to";
  token: string; // JWT token from localStorage or wherever it's stored
}
export interface DeleteAddressPayload {
  addressId: string | number;
  token: string; // JWT token from localStorage or wherever it's stored
}
export interface DoctorProfileApiResponse {
  status: number;
  success: boolean;
  data: {
    id: number;
    email: string;
    username: string;
    first_name: string;
    last_name: string;
    role_id: number;
    is_verified: boolean;
    profile_image: string | null;
    // Add more fields as needed based on actual API response
  };
  message: string;
}
export interface UpdateDoctorProfilePayload {
  first_name: string;
  last_name: string;
  email: string;
  username: string;
}

export interface RefinementResponse {
  status: number;
  success: boolean;
  data: {
    id: number;
    refinementDetails: string;
    upperImpression: string;
    lowerImpression: string;
    profileRepose: string;
    buccalRight: string;
    buccalLeft: string;
    frontalRepose: string;
    frontalSmiling: string;
    labialAnterior: string;
    occlussalLower: string;
    occlussalUpper: string;
    radioGraph1: string;
    radioGraph2: string;
    // Add more fields if API returns them
  };
  message: string;
}

// Helper function for submitting refinement request
export interface SubmitRefinementPayload {
  refinementDetails: string;
  upperImpression: File;
  lowerImpression: File;
  profileRepose: File;
  buccalRight: File;
  buccalLeft: File;
  frontalRepose: File;
  frontalSmiling: File;
  labialAnterior: File;
  occlussalLower: File;
  occlussalUpper: File;
  radioGraph1: File;
  radioGraph2: File;
}

export interface AlignerReplacementResponse {
  status: number;
  success: boolean;
  data?: string;
  message: string;
}

export interface RetainerRequestResponse {
  status: number;
  success: boolean;
  data?: unknown;
  message: string;
}
// Fetch a single employee by ID
export interface EmployeeApiResponse {
  status: number;
  success: boolean;
  data: {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
    username: string;
    profile_image: string | null;
    role: string;
    salutation: string;
    practice_phone_number: string;
    mobile: string;
    profession: string;
  };
  message: string;
}

export interface RetainerRequestPayload {
  patient_id: string | number;
  mode: "upload" | "use_last";
  other_details?: string;
  stlFile1: File | string | null;
  stlFile2: File | string | null;
  cbctFile?: File | string | null;
  profileRepose?: File | string | null;
  buccalRight?: File | string | null;
  buccalLeft?: File | string | null;
  frontalRepose?: File | string | null;
  frontalSmiling?: File | string | null;
  labialAnterior?: File | string | null;
  occlusalLower?: File | string | null;
  occlusalUpper?: File | string | null;
  radioGraph1?: File | string | null;
  radioGraph2?: File | string | null;
}
export interface UploadCbctPayload {
  patientId: string | number;
  cbctFile: File;
  reason: string;
}

export interface UploadCbctResponse {
  status: number;
  success: boolean;
  data?: unknown;
  message: string;
}
export interface PatientFileVersion {
  id: number;
  patient_id: number;
  created_by: number;
  version_number: number;
  title: string;
  status: string;
  rejection_reason: string;
  approval_reason: string;
  upper_steps: number;
  lower_steps: number;
  is_latest_version: boolean;
  data: unknown;
  created_at: string;
  shared_link: string;
  specialist_id: number;
}

// Specialist data type
export interface Specialist {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  profile_image: string | null;
  is_active: boolean;
  is_verified: boolean;
  created_at: string;
  updated_at: string;
}

// Specialist API response type
export interface SpecialistApiResponse {
  status: number;
  success: boolean;
  data: Specialist | null;
  message: string;
}
