import React, { useEffect, useRef, useState } from "react";
import { io, Socket } from "socket.io-client";
import { getToken, Decrytion } from '@/app/lib/auth';
import { FaUserMd, FaUserNurse } from 'react-icons/fa';
import { ImSpinner2 } from 'react-icons/im';
import { FiPaperclip } from 'react-icons/fi';
import DeleteConfirmationModal from '../reuseable/DeleteConfirmationModal';
import Image from 'next/image';
import { API_ROUTES } from '@/utils/ApiRoutes';
// import { 'next/navigation';

// Set SOCKET_URL to local backend
const SOCKET_URL = process.env.NEXT_PUBLIC_SOCKET_URL || "http://localhost:5008";

const MAX_FILE_SIZE = 50 * 1024 * 1024;
const ALLOWED_FILE_TYPES = [
  "application/zip", "application/x-zip", "application/x-zip-compressed",
  "application/octet-stream", "application/pdf",
  "image/jpeg", "image/png", "image/gif", "image/webp", "image/svg+xml"
];

// Add props interface
export interface ConversationsTabProps {
  currentUserId: number;
  patientId: number;
}

// Define Message interface
export interface Message {
  id: number;
  sender_id: number;
  patient_id: number;
  message: string;
  file_url?: string;
  file_name?: string;
  file_type?: string;
  file_size?: number;
  created_at: string;
  // Add any other fields as needed
}

const ConversationsTab: React.FC<ConversationsTabProps> = ({ currentUserId, patientId }) => {
  // const searchParams = useSearchParams();
  // const patientId = Number(searchParams.get('id'));
  const [messages, setMessages] = useState<Message[]>([]);
  const [messageInput, setMessageInput] = useState("");
  const [file, setFile] = useState<File | null>(null);
  const [filePreview, setFilePreview] = useState<React.ReactElement<{ src: string }> | string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [socket, setSocket] = useState<Socket | null>(null);
  const [receiverId, setReceiverId] = useState<number | null>(null);
  const [receiverLoading, setReceiverLoading] = useState(false);
  const [pendingMessage, setPendingMessage] = useState<{ text: string; file: File | null; filePreview: React.ReactNode } | null>(null);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [messageToDelete, setMessageToDelete] = useState<number | null>(null);
  const [errorModalOpen, setErrorModalOpen] = useState(false);
  const [errorModalMessage, setErrorModalMessage] = useState("");
  const [messagesLoading, setMessagesLoading] = useState(true);
  const [imageModalOpen, setImageModalOpen] = useState(false);
  const [modalImageUrl, setModalImageUrl] = useState<string | null>(null);
  const [expandedMessages, setExpandedMessages] = useState<{ [id: number]: boolean }>({});
  // Add state for file preview modal
  // Remove filePreviewModalOpen state (no modal needed)
  // const [filePreviewModalOpen, setFilePreviewModalOpen] = useState(false);

  // Fetch specialist (receiver) id when patientId changes
  useEffect(() => {
    if (!patientId) return;
    setReceiverLoading(true);
    const encryptedToken = getToken('AccessToken');
    const token = encryptedToken ? Decrytion(encryptedToken) : null;
    console.log('DEBUG patientId:', patientId);
    console.log('DEBUG token:', token);
    fetch(`${API_ROUTES.PATIENT.GET_SPECIALIST}/${patientId}/specialist`, {

      headers: {
        Authorization: `Bearer ${token}`,
      },
    })
      .then(res => res.json())
      .then(data => {
        console.log('DEBUG specialist API response:', data);
        if (data.success && data.data?.id) {
          setReceiverId(data.data.id);
        } else {
          setReceiverId(null);
        }
      })
      .catch((err) => { console.log('DEBUG specialist API error:', err); setReceiverId(null); })
      .finally(() => setReceiverLoading(false));
  }, [patientId]);

  // Connect socket on mount
  useEffect(() => {
    const s = io(SOCKET_URL);
    setSocket(s);
    if (currentUserId && patientId) {
      s.emit("addUser", currentUserId);
      s.emit("getConversationMessages", {
        user_id: currentUserId,
        target_user_id: receiverId,
        patient_id: patientId,
      });
    }
    s.on("messagesList", (data: Message[]) => {
      setMessages(data);
      setMessagesLoading(false);
    });
    s.on("newMessage", (message: Message) => {
      if (Number(message.patient_id) === Number(patientId)) {
        setMessages((prev) => [...prev, message]);
      }
    });
    s.on("messageDeleted", ({ messageId }) => {
      setMessages((prev) => prev.filter((msg) => msg.id !== messageId));
    });
    s.on("responseError", (errorMessage: string) => {
      console.log('SOCKET ERROR:', errorMessage);
      setErrorModalMessage(errorMessage);
      setErrorModalOpen(true);
    });
    return () => {
      s.disconnect();
    };
  }, [currentUserId, patientId, receiverId]);

  // Scroll to bottom on new messages
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  // Update pendingMessage when messageInput or file changes
  useEffect(() => {
    if (!messageInput && !file) {
      setPendingMessage(null);
      return;
    }
    setPendingMessage({ text: messageInput, file, filePreview });
  }, [messageInput, file, filePreview]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    if (file.size > MAX_FILE_SIZE) {
      setErrorModalMessage("File size exceeds the 50 MB limit. Please select a smaller file.");
      setErrorModalOpen(true);
      e.target.value = "";
      setFile(null);
      setFilePreview(null);
      return;
    }
    const fileExtension = file.name.split('.').pop()?.toLowerCase();
    let isAllowed = ALLOWED_FILE_TYPES.includes(file.type);
    if (!isAllowed && fileExtension === 'zip') isAllowed = true;
    if (!isAllowed) {
      setErrorModalMessage("File type not allowed. Please select a ZIP, PDF, or image file.");
      setErrorModalOpen(true);
      e.target.value = "";
      setFile(null);
      setFilePreview(null);
      return;
    }
    setFile(file);
    // Preview (no modal, just set preview state)
    if (file.type.startsWith("image/")) {
      const reader = new FileReader();
      reader.onload = (ev) => {
        setFilePreview(
          <Image
            src={ev.target?.result as string}
            alt="preview"
            width={48}
            height={48}
            unoptimized
            className="rounded object-cover border border-gray-200"
          />
        );
      };
      reader.readAsDataURL(file);
    } else if (file.type === "application/pdf") {
      setFilePreview(<div className="text-2xl">📄</div>);
    } else if (["application/zip", "application/x-zip-compressed", "application/x-zip"].includes(file.type) || fileExtension === 'zip') {
      setFilePreview(<div className="text-2xl">🗜️</div>);
    } else {
      setFilePreview(null);
    }
  };

  const clearFile = () => {
    setFile(null);
    setFilePreview(null);
    setPendingMessage((prev) => prev ? { ...prev, file: null, filePreview: null } : null);
    // No modal to close
  };

  const toBase64 = (file: File) =>
    new Promise<string>((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });

  const sendMessage = async (e?: React.FormEvent) => {
    if (e) e.preventDefault();
    if (!messageInput && !file) return;
    if (!currentUserId || !receiverId || !patientId) return;
    let fileBase64 = null, fileName = null, fileType = null, fileSize = null;
    if (file) {
      if (file.size > MAX_FILE_SIZE) {
        setErrorModalMessage("File size exceeds the 50 MB limit. Please select a smaller file.");
        setErrorModalOpen(true);
        clearFile();
        return;
      }
      const extension = file.name.split('.').pop()?.toLowerCase();
      let processedFileType = file.type;
      if (extension === 'zip' && (file.type === 'application/octet-stream' || !["application/zip", "application/x-zip-compressed", "application/x-zip"].includes(file.type))) {
        processedFileType = 'application/zip';
      }
      try {
        fileBase64 = await toBase64(file);
        fileName = file.name;
        fileType = processedFileType;
        fileSize = file.size;
      } catch {
        setErrorModalMessage("Failed to process the file. Please try again.");
        setErrorModalOpen(true);
        clearFile();
        return;
      }
    }
    console.log('SENDING MESSAGE:', {
      sender_id: currentUserId,
      receiver_id: receiverId,
      patient_id: patientId,
      message: messageInput,
      file_base64: fileBase64,
      file_name: fileName,
      file_type: fileType,
      file_size: fileSize,
    });
    socket?.emit("sendMessage", {
      sender_id: currentUserId,
      receiver_id: receiverId,
      patient_id: patientId,
      message: messageInput,
      file_base64: fileBase64,
      file_name: fileName,
      file_type: fileType,
      file_size: fileSize,
    });
    setMessageInput("");
    clearFile();
    setPendingMessage(null);
  };

  const deleteMessage = (messageId: number) => {
    // if (!window.confirm("Delete this message?")) return;
    if (!currentUserId || !receiverId || !patientId) return;
    socket?.emit("deleteMessage", {
      messageId,
      sender_id: currentUserId,
      receiver_id: receiverId,
      patient_id: patientId,
    });
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const handleDeleteClick = (messageId: number) => {
    setMessageToDelete(messageId);
    setDeleteModalOpen(true);
  };

  const handleCancelDelete = () => {
    setDeleteModalOpen(false);
    setMessageToDelete(null);
  };

  const handleConfirmDelete = () => {
    if (messageToDelete !== null) {
      deleteMessage(messageToDelete);
    }
    setDeleteModalOpen(false);
    setMessageToDelete(null);
  };

  const toggleExpandMessage = (id: number) => {
    setExpandedMessages((prev) => ({ ...prev, [id]: !prev[id] }));
  };

  return (
    <div className="p-2 sm:p-4 flex flex-col flex-grow relative ">
      {/* Overlay when no specialist is found */}
      {(!receiverId && !receiverLoading) && (
        <div className="absolute inset-0 bg-white/80 z-50 flex items-center justify-center pointer-events-auto">
          <div className="text-red-500 font-semibold text-base sm:text-lg text-center px-2">
            No specialist found for this patient.<br/>
            Conversation is disabled.
          </div>
        </div>
      )}
      <div className={`flex flex-col flex-grow ${(!receiverId && !receiverLoading) ? 'pointer-events-none opacity-60' : ''}`}>
        <div className="mb-2 sm:mb-4 flex justify-between items-center ">
          <h3 className="font-semibold text-gray-800 text-base sm:text-lg">Doctor & Specialist Chat</h3>
        </div>
        
        {receiverLoading && (
          <div className="flex items-center gap-2 text-orange-500 mb-2"><ImSpinner2 className="animate-spin" /> Loading specialist info...</div>
        )}
        {!receiverLoading && !receiverId && (
          <div className="text-red-500 mb-2">No specialist found for this patient.</div>
        )}
        <div className="flex-grow space-y-2 h-24  overflow-y-auto px-1 sm:px-2 pt-4 bg-gradient-to-br from-[#f9fafb] to-[#f3f4f6] border border-gray-200 rounded-2xl shadow-xl mb-2 transition-all duration-200">
          {messagesLoading ? (
            <div className="flex items-center justify-center h-full text-orange-500">
              <ImSpinner2 className="animate-spin w-8 h-8 mr-2" /> Loading messages...
            </div>        
          ) : (
            <>
              {(!messagesLoading && messages.length === 0 && !pendingMessage) && (
                <div className="text-center text-gray-400 pt-10 sm:pt-20 text-xs sm:text-base">No messages yet. Start the conversation!</div>
              )}
              {messages.map((convo) => {
                const isOwnMessage = convo.sender_id === currentUserId;
                // Determine your role for avatar
                // If you are the doctor, show doctor avatar for yourself, else specialist avatar
                // You can pass a prop like currentUserRole if needed, for now fallback to doctor avatar for own messages
                const ownAvatar = <div className="bg-primary text-white rounded-full w-8 h-8 sm:w-12 sm:h-12 flex items-center justify-center text-lg sm:text-2xl shadow-md border-2 border-white"><FaUserMd /></div>;
                const otherAvatar = <div className="bg-blue-500 text-white rounded-full w-8 h-8 sm:w-12 sm:h-12 flex items-center justify-center text-lg sm:text-2xl shadow-md border-2 border-white"><FaUserNurse /></div>;
                const senderLabel = isOwnMessage ? 'You' : 'Specialist';
                // Optionally, you can show Doctor/Specialist based on sender_id if you have that info
                const avatar = isOwnMessage ? ownAvatar : otherAvatar;
                const messageText = convo.message || "";
                const isLongMessage = messageText.length > 200;
                const isExpanded = expandedMessages[convo.id];
                const displayedMessage = isLongMessage && !isExpanded ? messageText.slice(0, 200) + '...' : messageText;
                return (
                  <div
                    key={convo.id}
                    className={`flex ${isOwnMessage ? 'justify-end items-end' : 'justify-start items-center'} fade-in-message`}
                    style={{ marginBottom: 12 }}
                  >
                    {!isOwnMessage && (
                      <div className="mr-1 sm:mr-2 z-10">{avatar}</div>
                    )}
                    <div className={`relative flex flex-col ${isOwnMessage ? 'items-end' : 'items-start'} group w-full`}>
                      <div
                        className={`relative w-fit max-w-[85vw] sm:max-w-[350px] md:max-w-[420px] min-w-[60px] sm:min-w-[80px] ${isOwnMessage ? 'bg-primary text-white' : 'bg-white text-gray-800'}
                          ${isOwnMessage ? 'rounded-2xl rounded-br-md' : 'rounded-2xl rounded-bl-md'}
                          px-3 py-2 sm:px-5 sm:py-3 shadow-lg transition-all duration-200
                          hover:scale-[1.025] hover:shadow-xl
                          ${isOwnMessage ? '' : 'border border-gray-200'}
                        `}
                        style={{ minWidth: 60 }}
                      >
                        {/* WhatsApp-style tail */}
                        {isOwnMessage ? (
                          <span className="hidden sm:block absolute -bottom-2 right-3 w-0 h-0 border-t-8 border-t-primary border-l-8 border-l-transparent border-r-0 border-b-0"></span>
                        ) : (
                          <span className="hidden sm:block absolute -bottom-2 left-3 w-0 h-0 border-t-8 border-t-white border-r-8 border-r-transparent border-l-0 border-b-0"></span>
                        )}
                        <div className="flex items-center gap-2 mb-1">
                          <span className="text-xs font-semibold opacity-80">{senderLabel}</span>
                        </div>
                        <div
                          className="text-xs sm:text-sm whitespace-pre-line"
                          style={{ wordBreak: 'break-all', overflowWrap: 'anywhere' }}
                        >
                          {displayedMessage}
                          {isLongMessage && (
                            <button
                              className="ml-2 text-white font-bold underline text-xs focus:outline-none"
                              style={{ textShadow: '0 1px 2px rgba(0,0,0,0.25)' }}
                              onClick={() => toggleExpandMessage(convo.id)}
                            >
                              {isExpanded ? 'See less' : 'See more'}
                            </button>
                          )}
                        </div>
                        {convo.file_url && (
                          <div className={`p-2 rounded mt-2 ${
                            convo.file_type?.startsWith('image/')
                              ? (isOwnMessage ? 'bg-primary/20' : 'bg-white')
                              : (isOwnMessage ? 'bg-primary/20 ' : 'bg-white border')
                          }`}>
                            {convo.file_type?.startsWith("image/") ? null : (
                              <>
                                <div className={`font-semibold text-xs ${isOwnMessage ? 'text-white' : 'text-gray-700'}`}>Attached file: {convo.file_name}</div>
                                <div className={`text-xs ${isOwnMessage ? 'text-white' : 'text-gray-500'}`}>File size: {formatFileSize(convo.file_size)} </div>
                              </>
                            )}
                            <a
                              href={typeof convo.file_url === 'string' ? `${SOCKET_URL}${convo.file_url}` : undefined}
                              className="inline-flex items-center gap-1 px-2 py-1 bg-blue-50 border border-blue-200 rounded text-blue-700 text-xs font-semibold hover:bg-blue-100 hover:text-blue-900 transition cursor-pointer shadow-sm mt-1 mb-1"
                              target="_blank"
                              rel="noopener noreferrer"
                              download={typeof convo.file_name === 'string' ? convo.file_name : undefined}
                              style={typeof convo.file_type === 'string' && convo.file_type.startsWith('image/') ? { display: 'none' } : {}}
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v2a2 2 0 002 2h12a2 2 0 002-2v-2M7 10l5 5m0 0l5-5m-5 5V4" />
                              </svg>
                              Download
                            </a>
                            {typeof convo.file_type === 'string' && convo.file_type.startsWith("image/") && typeof convo.file_url === 'string' && (
                              <Image
                                src={`${SOCKET_URL}${convo.file_url}`}
                                alt={typeof convo.file_name === 'string' ? convo.file_name : ''}
                                width={128}
                                height={160}
                                className="mt-2 rounded w-24 h-28 sm:w-32 sm:h-40 object-cover cursor-pointer border border-gray-200"
                                onClick={() => {
                                  setModalImageUrl(`${SOCKET_URL}${convo.file_url}`);
                                  setImageModalOpen(true);
                                }}
                              />
                            )}
                            {convo.file_type === "application/pdf" && <div className="mt-2">📄 PDF Document</div>}
                            {(convo.file_type === "application/zip" || convo.file_type === "application/x-zip-compressed" || (convo.file_name?.toLowerCase().endsWith('.zip'))) && (
                              <div className="mt-2 flex flex-col gap-1">
                                <div className="flex items-center gap-2">
                                  <svg xmlns="http://www.w3.org/2000/svg" className="w-7 h-7" fill="currentColor" viewBox="0 0 24 24">
                                    <rect x="3" y="3" width="18" height="18" rx="3" fill="#FBBF24" stroke="#B45309" strokeWidth="1.5" />
                                    <path d="M8 7h8M8 11h8M8 15h4" stroke="#B45309" strokeWidth="1.5" strokeLinecap="round" />
                                    <rect x="10.5" y="16" width="3" height="3" rx="0.7" fill="#B45309" />
                                  </svg>
                                  <span className={`font-semibold ${isOwnMessage ? 'text-white' : 'text-yellow-700'}`}>ZIP Archive</span>
                                </div>
                              </div>
                            )}
                          </div>
                        )}
                        {/* Delete button remains as previously updated */}
                        {isOwnMessage && (
                          <button
                            className="absolute top-1/2 -left-8 -translate-y-1/2 text-red-500 hover:text-red-700 bg-white/80 rounded-full p-1 shadow transition opacity-0 group-hover:opacity-100 focus:opacity-100 cursor-pointer border border-red-100"
                            onClick={() => handleDeleteClick(convo.id)}
                            title="Delete message"
                            style={{ zIndex: 2 }}
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6M1 7h22M8 7V5a2 2 0 012-2h2a2 2 0 012 2v2" />
                            </svg>
                          </button>
                        )}
                      </div>
                      {/* Timestamp below bubble, right/left aligned */}
                      <div className={`text-[10px] text-gray-400 select-none mt-1 ${isOwnMessage ? 'text-right pr-2' : 'text-left pl-2'}`}
                        style={{ minWidth: 60 }}
                      >
                        {new Date(convo.created_at).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                      </div>
                    </div>
                    {isOwnMessage && (
                      <div className="ml-1 sm:ml-2">{avatar}</div>
                    )}
                  </div>
                );
              })}
              <div ref={messagesEndRef} />
            </>
          )}
        </div>
        <form onSubmit={sendMessage} className="mt-2 flex gap-1 sm:gap-2 items-center bg-[#f8fafc] border border-gray-200 rounded-full px-2 sm:px-3 py-1.5 sm:py-2 shadow-md relative sticky bottom-0 z-20">
          <div className="flex items-center flex-grow relative gap-2">
            {/* Inline file preview inside input area */}
            {file && filePreview && (
              <div className="flex items-center gap-1 relative flex-shrink-0 max-w-[140px]">
                <div className="relative">
                  {filePreview}
                  <button
                    type="button"
                    className="absolute -top-2 -right-2 bg-white border border-gray-300 rounded-full w-5 h-5 flex items-center justify-center text-xs text-gray-600 hover:text-red-500 shadow"
                    onClick={clearFile}
                    title="Remove file"
                    tabIndex={-1}
                  >
                    &times;
                  </button>
                </div>
                <span className="text-xs truncate max-w-[80px] text-gray-700">{file.name}</span>
              </div>
            )}
            <input
              type="text"
              placeholder="Type your message..."
              value={messageInput}
              onChange={(e) => setMessageInput(e.target.value)}
              className="flex-grow p-2 border-none outline-none bg-transparent text-xs sm:text-sm"
              disabled={!receiverId || receiverLoading}
              style={{ minWidth: 0 }}
            />
          </div>
          <input
            type="file"
            id="fileInput"
            style={{ display: "none" }}
            onChange={handleFileChange}
            placeholder="Attach a file"
          />
          <button
            type="button"
            className="p-2 bg-gray-100 hover:bg-blue-200 rounded-full transition focus:outline-none focus:ring-2 focus:ring-blue-300"
            onClick={() => document.getElementById('fileInput')?.click()}
            title="Attach file"
            disabled={!receiverId || receiverLoading}
          >
            <FiPaperclip className="w-5 h-5 text-blue-500" />
          </button>
          <button
            type="submit"
            className="p-2 bg-primary text-white rounded-full hover:bg-orange-700 shadow transition focus:outline-none focus:ring-2 focus:ring-orange-300"
            title="Send message"
            disabled={!receiverId || receiverLoading}
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
            </svg>
          </button>
        </form>
        <DeleteConfirmationModal
          confirmModalOpen={deleteModalOpen}
          addressToDelete={messageToDelete !== null ? { type: 'billing', id: String(messageToDelete) } : null}
          cancelDeleteAddress={handleCancelDelete}
          confirmDeleteAddress={handleConfirmDelete}
          customMessage="Are you sure you want to delete this message?"
        />
        {errorModalOpen && (
          <div className="fixed inset-0 flex items-center justify-center bg-black/30 z-50">
            <div className="bg-white rounded-lg shadow-lg p-6 w-[90vw] max-w-[350px] flex flex-col gap-4">
              <h2 className="text-lg font-bold text-red-600">File Error</h2>
              <p className="text-gray-700">{errorModalMessage}</p>
              <button
                className="mt-2 px-4 py-2 bg-primary text-white rounded hover:bg-orange-700 transition"
                onClick={() => setErrorModalOpen(false)}
              >
                Close
              </button>
            </div>
          </div>
        )}
        {/* Image Modal Popup */}
        {imageModalOpen && modalImageUrl && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/70" onClick={() => setImageModalOpen(false)}>
            <div className="bg-white rounded-lg shadow-lg p-2 max-w-[95vw] max-w-2xl max-h-[90vh] flex flex-col items-center relative" onClick={e => e.stopPropagation()}>
              <button className="absolute top-2 right-2 text-gray-600 hover:text-red-500 text-2xl font-bold" onClick={() => setImageModalOpen(false)}>&times;</button>
              <Image src={modalImageUrl} alt="Preview" width={400} height={400} className="rounded max-h-[80vh] max-w-full object-contain" />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ConversationsTab; 