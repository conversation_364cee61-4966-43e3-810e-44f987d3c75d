import { fetchApi, getServerCookies } from '@/api/getapis';
import TreatmentOptions from '@/components/treatment-options/TreatmentOptions';
import { PatientFileData, PlansResponse } from '@/types/types';
import { API_ROUTES } from '@/utils/ApiRoutes';
import React from 'react';

export const dynamic = 'force-dynamic';

const Page = async () => {
  const response = await fetchApi(`${API_ROUTES.PLAN.GET_PLANS}`) as PlansResponse;
    const patientId = await getServerCookies("patientId");
    let patientData: PatientFileData | null = null;
  
    if (patientId) {
      const patientsArray = await fetchApi(`${API_ROUTES.PATIENT.GET_PATIENT_BY_ID}/${patientId}`);
      patientData = patientsArray as PatientFileData;
      console.log("🚀 ~ page ~ patientData:", patientData);
    } else {
      console.log("🚀 ~ page ~ patientData:", patientData);
    }
  
  return <TreatmentOptions data={response?.plans} patientData={patientData} />;
};

export default Page;
