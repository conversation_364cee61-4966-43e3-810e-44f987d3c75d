import { fetchApi } from "../getapis";

import { API_ROUTES } from "@/utils/ApiRoutes";
import { EmployeeApiResponse } from "@/types/types";

// Server-side fetch for employees list
export interface PaginatedEmployees {
  employees: unknown[];
  currentPage: number;
  perPage: number;
  totalItems: number;
  totalPages: number;
}

export const fetchEmployees = async (
  page: number = 1,
  limit: number = 10
): Promise<PaginatedEmployees | null> => {
  const url = `${API_ROUTES.EMPLOYEE.GET_EMPLOYEES}?page=${page}&limit=${limit}`;
  // The API returns { data: { data: EmployeeApiResponse[], ...pagination } }
  // fetchApi already returns the 'data' part of the response
  type EmployeesApiResponse = {
    data: EmployeeApiResponse[];
    currentPage: number;
    perPage: number;
    totalItems: number;
    totalPages: number;
  };
  const d = await fetchApi<EmployeesApiResponse>(url);
  if (!d) return null;
  return {
    employees: d.data || [],
    currentPage: d.currentPage || 1,
    perPage: d.perPage || 10,
    totalItems: d.totalItems || 0,
    totalPages: d.totalPages || 1,
  };
};

// Update employee (edit)
export const updateEmployee = async (
  employeeId: string,
  data: {
    first_name: string;
    last_name: string;
    email: string;
    salutation: string;
    practice_phone_number: string;
    mobile: string;
    profession: string;
  },
  token: string
): Promise<unknown> => {
  try {
    if (!token) {
      throw new Error('Authorization token is missing');
    }
    const formData = new FormData();
    formData.append('first_name', data.first_name);
    formData.append('last_name', data.last_name);
    formData.append('email', data.email);
    formData.append('salutation', data.salutation);
    formData.append('practice_phone_number', data.practice_phone_number);
    formData.append('mobile', data.mobile);
    formData.append('profession', data.profession);

    const response = await fetch(`${API_ROUTES.EMPLOYEE.GET_EMPLOYEES}/${employeeId}`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${token}`,
        'accept': 'application/json',
      },
      body: formData,
    });
    if (!response.ok) {
      let errorMsg = `Error updating employee: ${response.statusText}`;
      try {
        const errorData = await response.json();
        if (errorData && errorData.message) {
          errorMsg = errorData.message;
        }
      } catch {}
      throw new Error(errorMsg);
    }
    const result = await response.json();
    return result;
  } catch (error) {
    throw error;
  }
};

// Create employee (add)
export const createEmployee = async (
  data: {
    first_name: string;
    last_name: string;
    email: string;
    salutation: string;
    practice_phone_number: string;
    mobile: string;
    profession: string;
  },
  token: string
): Promise<unknown> => {
  try {
    if (!token) {
      throw new Error('Authorization token is missing');
    }
    const formData = new FormData();
    formData.append('first_name', data.first_name);
    formData.append('last_name', data.last_name);
    formData.append('email', data.email);
    formData.append('salutation', data.salutation);
    formData.append('practice_phone_number', data.practice_phone_number);
    formData.append('mobile', data.mobile);
    formData.append('profession', data.profession);

    const response = await fetch(API_ROUTES.EMPLOYEE.CREATE_EMPLOYEE, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'accept': 'application/json',
      },
      body: formData,
    });
    if (!response.ok) {
      let errorMsg = `Error creating employee: ${response.statusText}`;
      try {
        const errorData = await response.json();
        if (errorData && errorData.message) {
          errorMsg = errorData.message;
        }
      } catch {}
      throw new Error(errorMsg);
    }
    const result = await response.json();
    return result;
  } catch (error) {
    throw error;
  }
};

// Update employee status (active/inactive)
export const updateEmployeeStatus = async (
  employeeId: string,
  status: 'active' | 'inactive',
  token: string
): Promise<unknown> => {
  try {
    if (!token) {
      throw new Error('Authorization token is missing');
    }
    const body = new URLSearchParams();
    body.append('status', status);
    const response = await fetch(`${API_ROUTES.EMPLOYEE.GET_EMPLOYEES}/${employeeId}`, {
      method: 'PATCH',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/x-www-form-urlencoded',
        'accept': 'application/json',
      },
      body,
    });
    if (!response.ok) {
      let errorMsg = `Error updating employee status: ${response.statusText}`;
      try {
        const errorData = await response.json();
        if (errorData && errorData.message) {
          errorMsg = errorData.message;
        }
      } catch {}
      throw new Error(errorMsg);
    }
    const result = await response.json();
    return result;
  } catch (error) {
    throw error;
  }
};

// Server-side fetch for single employee by ID
export const fetchEmployeeById = async (
  id: string | number
): Promise<EmployeeApiResponse['data'] | null> => {
  const url = `${API_ROUTES.EMPLOYEE.GET_EMPLOYEE_BY_ID}/${id}`;
  const d = await fetchApi<EmployeeApiResponse>(url);
  return d?.data || null;
};
