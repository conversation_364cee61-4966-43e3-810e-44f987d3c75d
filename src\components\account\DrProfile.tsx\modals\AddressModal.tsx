import React, { useState } from "react";
// Removed unused Address import

type ModalData = {
  id?: string | number;
  clinic_name: string;
  street_address: string;
  city: string;
  postal_code: string;
  phone_number: string;
  address_type: string;
};

type Errors = {
  clinic_name?: string;
  street_address?: string;
  phone_number?: string;
  city?: string;
  postal_code?: string;
  [key: string]: string | undefined;
};

type Props = {
  modalOpen: boolean;
  modalType: "add" | "edit" | null;
  addressType: "ship_to" | "bill_to";
  modalData: ModalData;
  handleAddressChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleAddressSubmit: (e: React.FormEvent) => void;
  setModalOpen: (isOpen: boolean) => void;
};

const AddressModal: React.FC<Props> = ({
  modalOpen,
  modalType,
  addressType,
  modalData,
  handleAddressChange,
  handleAddressSubmit,
  setModalOpen,
}) => {
  const [errors, setErrors] = useState<Errors>({});

  if (!modalOpen) return null;

  const title = `${modalType === "add" ? "Add New" : "Update"} ${
    addressType === "ship_to" ? "Shipping" : "Billing"
  } Address`;

  // Custom validation
  const validate = () => {
    const newErrors: Errors = {};
    if (!modalData.clinic_name) newErrors.clinic_name = "Clinic name is required";
    if (!modalData.street_address) newErrors.street_address = "Street is required";
    if (!modalData.phone_number) newErrors.phone_number = "Phone is required";
    else if (!/^[0-9]{10,15}$/.test(modalData.phone_number))
      newErrors.phone_number = "Phone must be 10-15 digits";
    if (!modalData.city) newErrors.city = "City is required";
    if (!modalData.postal_code)
      newErrors.postal_code = "Postal code is required";
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const onSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    e.stopPropagation(); // Prevent bubbling to parent form
    if (validate()) {
      handleAddressSubmit(e);
      setErrors({});
    }
  };

  const handleCancel = () => {
    setErrors({});
    setModalOpen(false);
  };

  const handleFieldChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleAddressChange(e); // call parent to update modalData
    const { name, value } = e.target;
    // If there was an error for this field, re-validate it
    if (errors[name]) {
      let error = "";
      if (name === "clinic_name" && !value) error = "Clinic name is required";
      if (name === "street_address" && !value) error = "Street is required";
      if (name === "phone_number") {
        if (!value) error = "Phone is required";
        else if (!/^[0-9]{10,15}$/.test(value))
          error = "Phone must be 10-15 digits";
      }
      if (name === "city" && !value) error = "City is required";
      if (name === "postal_code" && !value) error = "Postal code is required";
      setErrors((prev: Errors) => ({ ...prev, [name]: error }));
    }
  };

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black/20 bg-opacity-30 z-50">
      <form
        onSubmit={onSubmit}
        className="bg-white rounded-lg shadow-lg p-6 w-[500px] flex flex-col gap-4"
      >
        <h2 className="text-lg font-semibold mb-2">{title}</h2>
        {modalType === "edit" && (
          <div className="text-xs text-gray-400 mb-2">ID: {modalData.id}</div>
        )}
        <input
          name="clinic_name"
          value={modalData.clinic_name}
          onChange={handleFieldChange}
          placeholder="Clinic Name"
          className={`border rounded px-3 py-2 ${
            errors.clinic_name ? "border-red-500" : "border-gray-300"
          }`}
        />
        {errors.clinic_name && (
          <div className="text-red-500 text-xs">{errors.clinic_name}</div>
        )}
        <input
          name="street_address"
          value={modalData.street_address}
          onChange={handleFieldChange}
          placeholder="Street"
          className={`border rounded px-3 py-2 ${
            errors.street_address ? "border-red-500" : "border-gray-300"
          }`}
        />
        {errors.street_address && (
          <div className="text-red-500 text-xs">{errors.street_address}</div>
        )}
        <input
          name="phone_number"
          value={modalData.phone_number}
          onChange={handleFieldChange}
          placeholder="Phone No."
          className={`border rounded px-3 py-2 ${
            errors.phone_number ? "border-red-500" : "border-gray-300"
          }`}
          type="number"
        />
        {errors.phone_number && (
          <div className="text-red-500 text-xs">{errors.phone_number}</div>
        )}
        <input
          name="city"
          value={modalData.city}
          onChange={handleFieldChange}
          placeholder="City"
          className={`border rounded px-3 py-2 ${
            errors.city ? "border-red-500" : "border-gray-300"
          }`}
        />
        {errors.city && (
          <div className="text-red-500 text-xs">{errors.city}</div>
        )}
        <input
          name="postal_code"
          value={modalData.postal_code}
          onChange={handleFieldChange}
          placeholder="Postal code"
          className={`border rounded px-3 py-2 ${
            errors.postal_code ? "border-red-500" : "border-gray-300"
          }`}
          type="number"
        />
        {errors.postal_code && (
          <div className="text-red-500 text-xs">{errors.postal_code}</div>
        )}
        <div className="flex justify-end gap-2 mt-4">
          <button
            type="button"
            className="px-4 py-2 cursor-pointer bg-gray-300 rounded hover:bg-gray-400"
            onClick={handleCancel}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 cursor-pointer bg-[#EB6309] text-white rounded hover:bg-[#D45A08]"
          >
            {modalType === "add" ? "Add" : "Update"}
          </button>
        </div>
      </form>
    </div>
  );
};

export default AddressModal;
